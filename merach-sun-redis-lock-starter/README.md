
## 使用

```java
// 基于注解
@RedisLockable(key = "#id", waitTime = 5)
public void echo(long id) {
    System.out.println("echo " + id);
}

@RedisLockable(value = "test", key = "#user.id", waitTime = 5)
public void echoV2(User user) {
    System.out.println("echo " + user.getId());
    
}

// 基于代码
public void handle(){
    lock = lockManager.getLock("test-lock");
    boolean locked = lock.tryLock();
    if(!locked){
        return;
    }

    try{
        // todo
    }finally{
        lock.unlock();
    }
}
```

---
## 版本变更
### 0.0.8-RELEASE
1. 基于redisson封装分布式锁