package com.merach.sun.redis.lock.provider;

import com.merach.sun.redis.lock.RedisLock;
import com.merach.sun.redis.lock.config.RedisLockProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.concurrent.TimeUnit;

@Slf4j
@RequiredArgsConstructor
public class RedissonRedisLock implements RedisLock {
    private final String lockName;
    private final RedisLockProperties config;
    private final RLock lock;

    public RedissonRedisLock(RedissonClient redissonClient, RedisLockProperties properties, String key) {
        this.config = properties;
        this.lockName = properties.getLockPrefix() + key;
        this.lock = redissonClient.getLock(lockName);
    }

    @Override
    public boolean tryLock() {
        return tryLock(0, TimeUnit.SECONDS);
    }

    @Override
    public boolean tryLock(long waitTime, TimeUnit unit) {
        long startTime = System.currentTimeMillis();
        long waitTimeMs = unit.toMillis(waitTime);

        try {
            boolean acquired = lock.tryLock(waitTimeMs, config.getLeaseTimeSec() * 1000, TimeUnit.MILLISECONDS);
            long costTime = System.currentTimeMillis() - startTime;

            if (acquired) {
                log.info("Successfully obtained lock. key={} costTime={}ms", lockName, costTime);
            } else {
                log.warn("Failed to obtain lock. key={} costTime={}ms", lockName, costTime);
            }

            return acquired;
        } catch (InterruptedException e) {
            long costTime = System.currentTimeMillis() - startTime;
            Thread.currentThread().interrupt();
            log.error("Failed to obtain lock. key={} costTime={}ms reason=exception:{} message={}",
                    lockName, costTime, e.getClass().getSimpleName(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void unlock() {
        try {
            if (isHeldByCurrentThread()) {
                lock.unlock();
                log.info("Successfully released lock. key={}", lockName);
            } else {
                log.warn("Discarded attempt to release lock. key={}", lockName);
            }
        } catch (Exception e) {
            log.error("Failed to release lock. key={} reason=exception:{} message={}",
                    lockName, e.getClass().getSimpleName(), e.getMessage(), e);
        }
    }

    @Override
    public int getHoldCount() {
        return lock.getHoldCount();
    }

    @Override
    public boolean isLocked() {
        boolean locked = lock.isLocked();
        if (log.isDebugEnabled()) {
            log.debug("Checking status. key={} locked={}", lockName, locked);
        }
        return locked;
    }

    @Override
    public boolean isHeldByCurrentThread() {
        return lock.isHeldByCurrentThread();
    }
}