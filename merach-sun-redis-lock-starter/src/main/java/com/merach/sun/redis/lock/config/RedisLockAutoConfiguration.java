package com.merach.sun.redis.lock.config;

import com.merach.sun.redis.lock.RedisLockManager;
import com.merach.sun.redis.lock.aspect.RedisLockAspect;
import com.merach.sun.redis.lock.provider.RedisLockManagerImpl;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@AutoConfigureAfter(RedisAutoConfiguration.class)
@EnableConfigurationProperties(RedisLockProperties.class)
public class RedisLockAutoConfiguration {

    @Bean
    public RedisLockAspect redisLockAspect(RedisLockManager redisLockManager) {
        return new RedisLockAspect(redisLockManager);
    }

    /**
     * todo 需要支持 自定义 redisson client
     * redis 配置可以和业务分开
     */
    @Bean
    @ConditionalOnMissingBean(RedissonClient.class)
    public RedissonClient redissonClient() {
        throw new UnsupportedOperationException("not support custom redisson client");
    }

    @Bean
    public RedisLockManager distributedLockManager(RedissonClient redissonClient, RedisLockProperties redisLockProperties) {
        log.info("initEvent=redisLock config={}", redisLockProperties);
        return new RedisLockManagerImpl(redissonClient, redisLockProperties);
    }
} 