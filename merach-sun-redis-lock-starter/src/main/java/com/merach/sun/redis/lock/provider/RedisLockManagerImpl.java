package com.merach.sun.redis.lock.provider;

import com.merach.sun.redis.lock.RedisLock;
import com.merach.sun.redis.lock.RedisLockManager;
import com.merach.sun.redis.lock.config.RedisLockProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;

@Slf4j
@RequiredArgsConstructor
public class RedisLockManagerImpl implements RedisLockManager {
    private final RedissonClient redissonClient;
    private final RedisLockProperties redisLockProperties;

    @Override
    public RedisLock getLock(String key) {
        return new RedissonRedisLock(redissonClient, redisLockProperties, key);
    }
}
