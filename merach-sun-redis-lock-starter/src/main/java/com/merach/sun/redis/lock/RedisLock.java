package com.merach.sun.redis.lock;

import java.util.concurrent.TimeUnit;

/**
 * redis 实现分布式锁
 */
public interface RedisLock {

    /**
     * 尝试获取锁 (不等待)
     *
     * @return 是否获取锁
     */
    boolean tryLock();

    /**
     * 尝试获取锁
     * @param waitTime 等待时间
     * @param unit 时间单位
     * @return 是否获取锁
     */
    boolean tryLock(long waitTime, TimeUnit unit);

    /**
     * 释放锁
     */
    void unlock();

    /**
     * 获取持有锁的线程数
     */
    int getHoldCount();

    /**
     * 获取锁状态
     */
    boolean isLocked();

    /**
     * 获取当前线程是否持有锁
     */
    boolean isHeldByCurrentThread();

    default LockGuard lockGuard(long waitTime, TimeUnit unit) {
        boolean locked = tryLock(waitTime, unit);
        return new LockGuard(this, locked);
    }
}
