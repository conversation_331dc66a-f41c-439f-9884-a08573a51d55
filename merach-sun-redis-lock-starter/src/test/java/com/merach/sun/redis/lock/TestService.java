package com.merach.sun.redis.lock;

import com.merach.sun.redis.lock.annotation.RedisLockable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

@Component
public class TestService {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class User {
        int id;
    }

    @RedisLockable(key = "#id", waitTime = 5)
    public void echo(long id) {
        System.out.println("echo " + id);
    }

    @RedisLockable(value = "test", key = "#user.id", waitTime = 5)
    public void echoV2(User user) {
        System.out.println("echo " + user.getId());
    }
}
