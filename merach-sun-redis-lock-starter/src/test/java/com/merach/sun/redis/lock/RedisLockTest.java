package com.merach.sun.redis.lock;

import com.merach.sun.redis.lock.config.TestRedisConfiguration;
import lombok.SneakyThrows;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(OutputCaptureExtension.class)
@SpringBootTest(classes = {TestApplication.class, TestRedisConfiguration.class})
public class RedisLockTest {

    @Autowired
    private RedisLockManager lockManager;

    @Test
    @SneakyThrows
    public void testLockBasicOperation() {
        var lock = lockManager.getLock("test-lock");
        assertTrue(lock.tryLock());
        assertTrue(lock.isLocked());

        // 其他线程尝试加锁
        CountDownLatch latch = new CountDownLatch(1);
        Executors.newSingleThreadExecutor().execute(() -> {
            var lock2 = lockManager.getLock("test-lock");
            assertFalse(lock2.tryLock());
            latch.countDown();
        });
        latch.await();

        // 当前线程解锁
        lock.unlock();
        assertFalse(lock.isLocked());
    }

    @Test
    public void testLockTimeout() throws InterruptedException {
        var lock = lockManager.getLock("test-lock-timeout");

        assertTrue(lock.tryLock(5, TimeUnit.SECONDS));
        assertTrue(lock.isLocked());

        var lock2 = lockManager.getLock("test-lock-timeout");
        assertTrue(lock2.tryLock(1, TimeUnit.SECONDS));
        assertEquals(2, lock2.getHoldCount());

        lock.unlock();
        assertEquals(1, lock2.getHoldCount());

        assertTrue(lock2.tryLock(1, TimeUnit.SECONDS));
        lock2.unlock();
        lock2.unlock();
        assertEquals(0, lock2.getHoldCount());
    }

    @Test
    public void testConcurrentAccess() throws InterruptedException {
        int threadCount = 10;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch endLatch = new CountDownLatch(threadCount);
        AtomicInteger counter = new AtomicInteger(0);

        for (int i = 0; i < threadCount; i++) {
            new Thread(() -> {
                try {
                    startLatch.await();
                    var lock = lockManager.getLock("test-lock-concurrent");
                    if (lock.tryLock(5, TimeUnit.SECONDS)) {
                        try {
                            counter.incrementAndGet();
                            Thread.sleep(100);
                        } finally {
                            lock.unlock();
                        }
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    endLatch.countDown();
                }
            }).start();
        }

        startLatch.countDown();
        endLatch.await();

        assertEquals(threadCount, counter.get());
    }

    @Autowired
    private TestService testService;

    @Test
    public void testAop(CapturedOutput capturedOutput) {
        testService.echo(1L);
        assertThat(capturedOutput.toString()).contains("echo 1");

        testService.echoV2(new TestService.User(2));
        assertThat(capturedOutput.toString()).contains("echo 2");
    }
}