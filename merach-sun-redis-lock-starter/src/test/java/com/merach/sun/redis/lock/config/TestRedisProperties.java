package com.merach.sun.redis.lock.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
public class TestRedisProperties {
    private int redisPort;
    private String redisHost;

    public TestRedisProperties(
      @Value("${spring.data.redis.port}") int redisPort,
      @Value("${spring.data.redis.host}") String redisHost) {
        this.redisPort = redisPort;
        this.redisHost = redisHost;
    }
}