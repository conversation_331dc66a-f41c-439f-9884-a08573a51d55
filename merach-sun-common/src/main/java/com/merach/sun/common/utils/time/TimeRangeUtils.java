package com.merach.sun.common.utils.time;

import lombok.experimental.UtilityClass;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;

/**
 * 时间范围工具类，提供获取时间范围（如天、周、月的起止时间）、时间区间判断和时间差计算等功能
 *
 * <AUTHOR>
 */
@UtilityClass
public class TimeRangeUtils {
    /**
     * 获取指定日期的起始时间（精确到秒）：00:00:00
     *
     * @param date 指定日期
     * @return 该日期的起始时间
     */
    public static LocalDateTime startOfDay(LocalDate date) {
        return date.atStartOfDay();
    }

    /**
     * 获取今天的起始时间（00:00:00）
     *
     * @return 今天的起始时间
     */
    public static LocalDateTime startOfToday() {
        return startOfDay(LocalDate.now());
    }

    /**
     * 获取指定日期的结束时间（精确到秒）：23:59:59
     *
     * @param date 指定日期
     * @return 该日期的结束时间
     */
    public static LocalDateTime endOfDay(LocalDate date) {
        return date.atTime(LocalTime.MAX.withNano(0));
    }

    /**
     * 获取今天的结束时间（23:59:59）
     *
     * @return 今天的结束时间
     */
    public static LocalDateTime endOfToday() {
        return endOfDay(LocalDate.now());
    }

    /**
     * 获取指定日期所在周的开始时间（周一 00:00:00）
     *
     * @param date 指定日期
     * @return 所在周的开始时间
     */
    public static LocalDateTime startOfWeek(LocalDate date) {
        return date.with(DayOfWeek.MONDAY).atStartOfDay();
    }

    /**
     * 获取指定日期所在周的结束时间（周日 23:59:59）
     *
     * @param date 指定日期
     * @return 所在周的结束时间
     */
    public static LocalDateTime endOfWeek(LocalDate date) {
        return date.with(DayOfWeek.SUNDAY).atTime(LocalTime.MAX.withNano(0));
    }

    /**
     * 获取指定日期所在月的开始时间（1号 00:00:00）
     *
     * @param date 指定日期
     * @return 所在月的开始时间
     */
    public static LocalDateTime startOfMonth(LocalDate date) {
        return date.with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay();
    }

    /**
     * 获取指定日期所在月的结束时间（月末 23:59:59）
     *
     * @param date 指定日期
     * @return 所在月的结束时间
     */
    public static LocalDateTime endOfMonth(LocalDate date) {
        return date.with(TemporalAdjusters.lastDayOfMonth()).atTime(LocalTime.MAX.withNano(0));
    }

    /**
     * 判断两个时间是否在同一天
     *
     * @param t1 第一个时间
     * @param t2 第二个时间
     * @return 如果两个时间在同一天返回true，否则返回false
     */
    public static boolean isSameDay(LocalDateTime t1, LocalDateTime t2) {
        return t1.toLocalDate().isEqual(t2.toLocalDate());
    }

    /**
     * 判断指定时间是否在某个时间区间内（包含边界）
     *
     * @param time 要判断的时间
     * @param start 区间开始时间
     * @param end 区间结束时间
     * @return 如果在区间内返回true，否则返回false
     */
    public static boolean isBetween(LocalDateTime time, LocalDateTime start, LocalDateTime end) {
        return !time.isBefore(start) && !time.isAfter(end);
    }

    public static boolean isLessThanOrEqualTo(LocalDate date, LocalDate target) {
        return date.isBefore(target) || date.isEqual(target);
    }

    public static boolean isGreaterThanOrEqualTo(LocalDate date, LocalDate target) {
        return date.isAfter(target) || date.isEqual(target);
    }

    /**
     * 计算两个时间之间相差的天数
     *
     * @param start 开始时间
     * @param end 结束时间
     * @return 相差的天数（如果end在start之后，返回正数；否则返回负数）
     */
    public static long betweenDays(LocalDateTime start, LocalDateTime end) {
        return ChronoUnit.DAYS.between(start, end);
    }

    /**
     * 计算两个时间之间相差的小时数
     *
     * @param start 开始时间
     * @param end 结束时间
     * @return 相差的小时数（如果end在start之后，返回正数；否则返回负数）
     */
    public static long betweenHours(LocalDateTime start, LocalDateTime end) {
        return ChronoUnit.HOURS.between(start, end);
    }

    /**
     * 计算两个时间之间相差的分钟数
     *
     * @param start 开始时间
     * @param end 结束时间
     * @return 相差的分钟数（如果end在start之后，返回正数；否则返回负数）
     */
    public static long betweenMinutes(LocalDateTime start, LocalDateTime end) {
        return ChronoUnit.MINUTES.between(start, end);
    }

    /**
     * 计算两个时间之间相差的秒数
     *
     * @param start 开始时间
     * @param end 结束时间
     * @return 相差的秒数（如果end在start之后，返回正数；否则返回负数）
     */
    public static long betweenSeconds(LocalDateTime start, LocalDateTime end) {
        return ChronoUnit.SECONDS.between(start, end);
    }
}
