package com.merach.sun.common.error;

/**
 * 错误标记接口
 * <p>
 * 该接口定义了错误信息的标准结构，用于统一管理系统中的错误码和错误消息。
 * 实现该接口的类或枚举可以提供标准化的错误信息，便于异常处理和错误响应。
 * </p>
 *
 * <p>使用示例：</p>
 * <pre>{@code
 * public enum UserErrorEnum implements ErrorMarker {
 *     USER_NOT_FOUND("user_not_found", "用户不存在"),
 *     USER_DISABLED("user_disabled", "用户已禁用");
 *
 *     private final String errorCode;
 *     private final String errorMsg;
 *
 *     // 构造方法和getter方法...
 * }
 * }</pre>
 *
 * <AUTHOR>
 * @since 2025-09-11 11:36
 * @see com.merach.sun.common.error.enums.CommonErrorMarkerEnum
 * @see com.merach.sun.common.exception.BizException
 * @see com.merach.sun.common.exception.SysException
 */
public interface ErrorMarker {

    /**
     * 获取错误码
     * <p>
     * 错误码应该是唯一的标识符，用于程序化处理错误。
     * 建议使用有意义的字符串，如 "user_not_found"、"param_invalid" 等。
     * </p>
     *
     * @return 错误码，不能为null
     */
    String getErrorCode();

    /**
     * 获取错误消息
     * <p>
     * 错误消息是面向用户的可读描述，用于展示给最终用户或记录日志。
     * 应该提供清晰、准确的错误描述信息。
     * </p>
     *
     * @return 错误消息，不能为null
     */
    String getErrorMsg();
}
