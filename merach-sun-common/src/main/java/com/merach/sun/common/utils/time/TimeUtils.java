package com.merach.sun.common.utils.time;

import lombok.experimental.UtilityClass;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 时间工具类，提供时间加减和截断操作
 *
 * <AUTHOR>
 */
@UtilityClass
public class TimeUtils {

    // ===== 时间加减 =====
    /**
     * 在给定时间上增加指定天数
     *
     * @param time 基准时间
     * @param days 要增加的天数，可以为负数
     * @return 增加天数后的新时间
     */
    public static LocalDateTime addDays(LocalDateTime time, long days) {
        return time.plusDays(days);
    }

    /**
     * 在给定时间上增加指定小时数
     *
     * @param time 基准时间
     * @param hours 要增加的小时数，可以为负数
     * @return 增加小时数后的新时间
     */
    public static LocalDateTime addHours(LocalDateTime time, long hours) {
        return time.plusHours(hours);
    }

    /**
     * 在给定时间上增加指定分钟数
     *
     * @param time 基准时间
     * @param minutes 要增加的分钟数，可以为负数
     * @return 增加分钟数后的新时间
     */
    public static LocalDateTime addMinutes(LocalDateTime time, long minutes) {
        return time.plusMinutes(minutes);
    }

    // ========== 时间截断 ==========
    /**
     * 将时间截断到秒级别（去除毫秒部分）
     *
     * @param time 要截断的时间
     * @return 截断到秒级别的时间
     */
    public static LocalDateTime truncateToSeconds(LocalDateTime time) {
        return time.truncatedTo(ChronoUnit.SECONDS);
    }
}