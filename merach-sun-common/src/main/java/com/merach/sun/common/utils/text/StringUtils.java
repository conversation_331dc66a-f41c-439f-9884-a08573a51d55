package com.merach.sun.common.utils.text;


import lombok.experimental.UtilityClass;

/**
 * String 工具类
 */
@UtilityClass
public final class StringUtils {

    /**
     * 判断字符串中是否全是空白字符
     */
    public static boolean isBlank(String s) {
        return s == null || s.isBlank();
    }

    public static boolean isNotBlank(String cs) {
        return !isBlank(cs);
    }

    /**
     * 判断字符串中是否为空
     */
    public static boolean isEmpty(String s) {
        return s == null || s.isEmpty();
    }

    public static boolean isNotEmpty(String cs) {
        return !isEmpty(cs);
    }

    /**
     * 对象转为字符串去除左右空格
     */
    public static String trimToString(Object o) {
        return String.valueOf(o).trim();
    }

}
