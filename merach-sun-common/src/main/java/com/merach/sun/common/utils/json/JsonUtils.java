package com.merach.sun.common.utils.json;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.experimental.UtilityClass;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Jackson 工具类，封装常用 JSON 操作
 * <AUTHOR>
 * @since 2025-09-11 11:30
 */
@UtilityClass
public class JsonUtils {

    private static final ObjectMapper MAPPER = new ObjectMapper();

    static {
        MAPPER.registerModule(new Jdk8Module());
        MAPPER.registerModule(new JavaTimeModule());
        MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        MAPPER.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        MAPPER.setDateFormat(sdf);
    }

    /**
     * 将对象序列化为 JSON 字符串。
     *
     * @param obj 要序列化的对象
     * @return JSON 字符串
     */
    public static String toJsonString(Object obj) {
        try {
            return MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Jackson 序列化失败", e);
        }
    }

    /**
     * 将 JSON 字符串反序列化为指定类型的对象。
     *
     * @param json  JSON 字符串
     * @param clazz 目标类型
     * @return 反序列化后的对象
     */
    public static <T> T parseObject(String json, Class<T> clazz) {
        try {
            return MAPPER.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Jackson 反序列化失败", e);
        }
    }

    /**
     * 将 JSON 字符串反序列化为 List 类型。
     *
     * @param json        JSON 字符串
     * @param elementType 集合元素类型
     * @return 反序列化后的 List
     */
    public static <T> List<T> parseArray(String json, Class<T> elementType) {
        try {
            return MAPPER.readValue(json,
                    MAPPER.getTypeFactory().constructCollectionType(ArrayList.class, elementType));
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Jackson 反序列化失败", e);
        }
    }

    /**
     * 将 JSON 字符串反序列化为 Map 类型。
     *
     * @param json JSON 字符串
     * @return 反序列化后的 Map
     */
    public static Map<String, Object> parseMap(String json) {
        try {
            return MAPPER.readValue(json, Map.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Jackson 反序列化失败", e);
        }
    }

    /**
     * 将 JSON 字符串反序列化为复杂泛型类型。
     *
     * @param json          JSON 字符串
     * @param typeRef 泛型类型引用，如：new TypeReference<List<User>>() {}
     * @return 反序列化后的对象
     */
    public static <T> T parseObject(String json, TypeReference<T> typeRef) {
        try {
            return MAPPER.readValue(json, typeRef);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Jackson 反序列化失败", e);
        }
    }
}