package com.merach.sun.common.utils.time;

import lombok.experimental.UtilityClass;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * 时间转换工具类，提供了在 Date、LocalDateTime、时间戳之间的相互转换功能
 * 主要用于处理遗留系统中的 Date 类型和时间戳的转换需求
 *
 * <AUTHOR>
 */
@UtilityClass
public class TimeConverterUtils {

    /**
     * 将 LocalDateTime 转换为 java.util.Date
     * 转换时使用系统默认时区
     *
     * @param time LocalDateTime 时间
     * @return Date 对象
     */
    public static Date toDate(LocalDateTime time) {
        return Date.from(time.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 将 java.util.Date 转换为 LocalDateTime
     * 转换时使用系统默认时区
     *
     * @param date Date 对象
     * @return LocalDateTime 时间
     */
    public static LocalDateTime fromDate(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    /**
     * 将时间戳（毫秒）转换为 LocalDateTime
     * 转换时使用系统默认时区
     *
     * @param timestampMillis 时间戳，单位：毫秒
     * @return LocalDateTime 时间
     */
    public static LocalDateTime fromTimestamp(long timestampMillis) {
        return Instant.ofEpochMilli(timestampMillis)
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
    }

    /**
     * 将 LocalDateTime 转换为时间戳（毫秒）
     * 转换时使用系统默认时区
     *
     * @param time LocalDateTime 时间
     * @return 时间戳，单位：毫秒
     */
    public static long toTimestamp(LocalDateTime time) {
        return time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }
}
