package com.merach.sun.common.exception;

import com.merach.sun.common.error.ErrorMarker;

/**
 * 系统异常类
 * <AUTHOR>
 * @since 2025-09-11 11:36
 */
public class SysException extends BaseException {

    /**
     * 系统异常状态码
     */
    private static final int STATUS = 500;

    @Deprecated
    private static final String DEFAULT_ERR_CODE = "SYS_ERROR";

    @Deprecated
    public SysException(String errMessage) {
        super(DEFAULT_ERR_CODE, errMessage);
    }

    @Deprecated
    public SysException(String errCode, String errMessage) {
        super(errCode, errMessage);
    }

    @Deprecated
    public SysException(String errMessage, Throwable e) {
        super(DEFAULT_ERR_CODE, errMessage, e);
    }

    @Deprecated
    public SysException(String errorCode, String errMessage, Throwable e) {
        super(errorCode, errMessage, e);
    }

    public SysException(ErrorMarker errorMarker) {
        super(errorMarker.getErrorCode(), errorMarker.getErrorMsg());
    }

    public int getStatus() {
        return STATUS;
    }
}
