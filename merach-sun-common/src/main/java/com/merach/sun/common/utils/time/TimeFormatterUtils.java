package com.merach.sun.common.utils.time;

import lombok.experimental.UtilityClass;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;


/**
 * 时间格式化工具类，提供时间格式化和时间间隔描述功能
 *
 * <AUTHOR>
 */
@UtilityClass
public class TimeFormatterUtils {

    /**
     * 使用默认格式（yyyy-MM-dd HH:mm:ss）格式化时间
     *
     * @param time 要格式化的时间
     * @return 格式化后的时间字符串
     */
    public static String format(LocalDateTime time) {
        return time.format(DatePattern.NORM_DATETIME_FORMATTER);
    }

    /**
     * 使用带毫秒的格式（yyyy-MM-dd HH:mm:ss.SSS）格式化时间
     *
     * @param time 要格式化的时间
     * @return 格式化后的时间字符串
     */
    public static String formatMillis(LocalDateTime time) {
        return time.format(DatePattern.NORM_DATETIME_MS_FORMATTER);
    }

    /**
     * 使用指定的格式模式格式化时间
     *
     * @param time 要格式化的时间
     * @param pattern 格式模式，如："yyyy年MM月dd日 HH时mm分ss秒"
     * @return 格式化后的时间字符串
     */
    public static String format(LocalDateTime time, String pattern) {
        return time.format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 将持续时间转换为人类可读的字符串表示
     * 例如：2天3小时45分钟30秒 -> "2d3h45m30s"
     *
     * @param duration 持续时间
     * @return 格式化后的持续时间字符串
     */
    public static String describeDuration(Duration duration) {
        long days = duration.toDays();
        duration = duration.minusDays(days);
        long hours = duration.toHours();
        duration = duration.minusHours(hours);
        long minutes = duration.toMinutes();
        duration = duration.minusMinutes(minutes);
        long seconds = duration.getSeconds();

        StringBuilder sb = new StringBuilder();
        if (days > 0) {
            sb.append(days).append("d");
        }
        if (hours > 0) {
            sb.append(hours).append("h");
        }
        if (minutes > 0) {
            sb.append(minutes).append("m");
        }
        if (seconds > 0 || sb.length() == 0) {
            sb.append(seconds).append("s");
        }
        return sb.toString();
    }

    /**
     * 计算并描述两个时间点之间的时间差
     * 返回的格式同 describeDuration，但会自动取绝对值
     *
     * @param from 起始时间
     * @param to 结束时间
     * @return 格式化后的时间差字符串
     */
    public static String describeTimeDiff(LocalDateTime from, LocalDateTime to) {
        return describeDuration(Duration.between(from, to).abs());
    }
}
