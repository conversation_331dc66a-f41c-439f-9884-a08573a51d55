package com.merach.sun.common.exception;

import com.merach.sun.common.error.ErrorMarker;

/**
 * 业务异常类
 * <AUTHOR>
 * @since 2025-09-11 11:36
 */
public class BizException extends BaseException {

    /**
     * 业务异常状态码
     */
    private static final int STATUS = 601;

    @Deprecated
    private static final String DEFAULT_ERR_CODE = "BIZ_ERROR";

    @Deprecated
    public BizException(String errMessage) {
        super(DEFAULT_ERR_CODE, errMessage);
    }

    @Deprecated
    public BizException(String errCode, String errMessage) {
        super(errCode, errMessage);
    }

    @Deprecated
    public BizException(String errMessage, Throwable e) {
        super(DEFAULT_ERR_CODE, errMessage, e);
    }

    @Deprecated
    public BizException(String errorCode, String errMessage, Throwable e) {
        super(errorCode, errMessage, e);
    }

    public BizException(ErrorMarker errorMarker) {
        super(errorMarker.getErrorCode(), errorMarker.getErrorMsg());
    }

    public int getStatus() {
        return STATUS;
    }
}
