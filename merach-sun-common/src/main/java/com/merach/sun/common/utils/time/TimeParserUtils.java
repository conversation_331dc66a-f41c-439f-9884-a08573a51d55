package com.merach.sun.common.utils.time;

import lombok.experimental.UtilityClass;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 时间解析工具类，提供字符串到 LocalDateTime 的解析功能
 *
 * <AUTHOR>
 */
@UtilityClass
public class TimeParserUtils {

    /**
     * 使用指定格式解析时间字符串
     *
     * @param text 时间字符串
     * @param format 日期格式
     * @return LocalDateTime 对象
     * @throws DateTimeParseException 如果解析失败
     */
    public static LocalDateTime parse(String text, String format) {
        return LocalDateTime.parse(text, DateTimeFormatter.ofPattern(format));
    }

    /**
     * 智能解析时间字符串，支持默认格式和毫秒格式
     *
     * @param text 时间字符串
     * @return LocalDateTime 对象
     * @throws DateTimeParseException 如果解析失败
     */
    public static LocalDateTime parse(String text) {
        // todo
        throw new UnsupportedOperationException("not implemented");
    }
}
