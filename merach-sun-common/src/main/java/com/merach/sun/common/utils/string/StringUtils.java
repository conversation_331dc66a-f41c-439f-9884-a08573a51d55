package com.merach.sun.common.utils.string;


import lombok.experimental.UtilityClass;

/**
 * 字符串工具类
 * <AUTHOR>
 * @since 2025-09-11 11:30
 */
@UtilityClass
public final class StringUtils {

    /**
     * 字符串判空
     * 如果字符串为 null 或者 "" 或者 " " 则返回 true
     */
    public static boolean isBlank(String s) {
        return s == null || s.isBlank();
    }

    /**
     * 字符串不为空
     * 如果字符串为 null 或者 "" 或者 " " 则返回 false
     */
    public static boolean isNotBlank(String cs) {
        return !isBlank(cs);
    }

    /**
     * 判断字符串中是否为空
     * 如果字符串为 null 或者 "" 则返回 true
     */
    public static boolean isEmpty(String s) {
        return s == null || s.isEmpty();
    }

    /**
     * 判断字符串中是否不为空
     * 如果字符串为 null 或者 "" 则返回 false
     */
    public static boolean isNotEmpty(String cs) {
        return !isEmpty(cs);
    }
}
