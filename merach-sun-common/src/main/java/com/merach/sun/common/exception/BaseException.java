package com.merach.sun.common.exception;

/**
 * 异常基类
 * <AUTHOR>
 * @since 2025-09-11 11:36
 */
public abstract class BaseException extends RuntimeException {

    private String errCode;

    public BaseException(String errCode, String errMessage) {
        super(errMessage);
        this.errCode = errCode;
    }

    public BaseException(String errCode, String errMessage, Throwable e) {
        super(errMessage, e);
        this.errCode = errCode;
    }

    public String getErrCode() {
        return errCode;
    }

    public void setErrCode(String errCode) {
        this.errCode = errCode;
    }
}
