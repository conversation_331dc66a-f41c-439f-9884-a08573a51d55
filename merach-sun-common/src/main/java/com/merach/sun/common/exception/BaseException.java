package com.merach.sun.common.exception;

import lombok.Getter;

/**
 * 异常基类
 * <AUTHOR>
 * @since 2025-09-11 11:36
 */
@Getter
public abstract class BaseException extends RuntimeException {

    private final String errCode;

    public BaseException(String errCode, String errMessage) {
        super(errMessage);
        this.errCode = errCode;
    }

    public BaseException(String errCode, String errMessage, Throwable e) {
        super(errMessage, e);
        this.errCode = errCode;
    }

    public String getErrMsg() {
        return this.getMessage();
    }
}
