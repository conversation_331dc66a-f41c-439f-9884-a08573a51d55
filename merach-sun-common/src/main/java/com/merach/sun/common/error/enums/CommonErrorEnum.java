package com.merach.sun.common.error.enums;

import com.merach.sun.common.error.ErrorMarker;

/**
 * 通用错误码
 * <AUTHOR>
 * @since 2025-09-11 11:41
 */
public enum CommonErrorEnum implements ErrorMarker {

    SYSTEM_ERROR("system_error","系统错误"),
    PARAM_ERROR("param_error","参数错误"),
    UNKNOWN_ERROR("unknown_error", "未知错误"),
    ;

    private final String errorCode;

    private final String errorMsg;

    CommonErrorEnum(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    @Override
    public String getErrorCode() {
        return this.errorCode;
    }

    @Override
    public String getErrorMsg() {
        return this.errorMsg;
    }
}
