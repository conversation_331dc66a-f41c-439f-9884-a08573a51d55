package com.merach.sun.common.utils.validator;

import com.merach.sun.common.error.ErrorMarker;
import com.merach.sun.common.exception.BizException;
import com.merach.sun.common.utils.collection.CollectionUtils;
import com.merach.sun.common.utils.string.StringUtils;
import lombok.experimental.UtilityClass;

import java.util.Collection;
import java.util.Objects;

/**
 * 断言工具类
 * <AUTHOR>
 * @since 2025-09-11 11:30
 */
@UtilityClass
public class AssertUtil {

    /**
     * 断言对象不为 null
     * @param obj 断言对象
     * @param errorMarker 错误信息
     */
    public static void notNull(Object obj, ErrorMarker errorMarker) {
        if (Objects.isNull(obj)) {
            throw new BizException(errorMarker);
        }
    }

    /**
     * 断言字符串不为空
     * @param str 断言字符串
     * @param errorMarker 错误信息
     */
    public static void notBlank(String str, ErrorMarker errorMarker) {
        if (StringUtils.isBlank(str)) {
            throw new BizException(errorMarker);
        }
    }

    /**
     * 断言集合不为空
     * @param collection 断言集合
     * @param errorMarker 错误信息
     */
    public static <T> void notEmpty(Collection<T> collection, ErrorMarker errorMarker) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new BizException(errorMarker);
        }
    }

    /**
     * 断言表达式为 true
     * @param expression 断言表达式
     * @param errorMarker 错误信息
     */
    public static void mustTrue(boolean expression, ErrorMarker errorMarker) {
        if (!expression) {
            throw new BizException(errorMarker);
        }
    }
}
