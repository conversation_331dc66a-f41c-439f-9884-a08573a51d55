package com.merach.sun.common.utils.object;

import org.springframework.objenesis.instantiator.util.ClassUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.toList;

/**
 * Bean 转换工具类
 */
public final class BeanUtils {

    private BeanUtils() {
    }

    public static Map<String, Object> beanToMap(Object bean) {
        if (bean == null) {
            return null;
        }

        Map<String, Object> target = new LinkedHashMap<>();
        org.springframework.beans.BeanUtils.copyProperties(bean, target);
        return target;
    }

    public static <T> T mapToBean(Map<String, ?> map, Class<T> clazz) {
        T bean = ClassUtils.newInstance(clazz);
        org.springframework.beans.BeanUtils.copyProperties(map, bean, clazz);
        return bean;
    }

    public static <T> List<Map<String, Object>> beansToMaps(List<T> beans) {
        if (CollectionUtils.isEmpty(beans)) {
            return Collections.emptyList();
        }
        return beans.stream().map(BeanUtils::beanToMap).collect(toList());
    }

    public static <T> List<T> mapsToBeans(List<? extends Map<String, ?>> maps, Class<T> clazz) {
        if (CollectionUtils.isEmpty(maps)) {
            return Collections.emptyList();
        }
        return maps.stream().map(e -> mapToBean(e, clazz)).collect(toList());
    }

    public static <T> T copyToBean(Object source, Class<T> clazz) {
        T target = ClassUtils.newInstance(clazz);
        org.springframework.beans.BeanUtils.copyProperties(source, target);
        return target;
    }

    public static <T> List<T> copyToBeanList(List<?> source, Class<T> clazz) {
        if (CollectionUtils.isEmpty(source)) {
            return Collections.emptyList();
        }
        return source.stream().map(e -> copyToBean(e, clazz)).collect(toList());
    }
}