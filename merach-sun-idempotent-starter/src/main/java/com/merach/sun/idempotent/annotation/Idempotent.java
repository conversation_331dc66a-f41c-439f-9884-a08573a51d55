package com.merach.sun.idempotent.annotation;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

@Inherited
@Target(ElementType.METHOD)
@Retention(value = RetentionPolicy.RUNTIME)
public @interface Idempotent {
    String KEY_PREFIX = "idem";

    /**
     * 标识位，用来区分服务或者业务
     *
     * @return String
     */
    String prefix() default KEY_PREFIX;

    /**
     * 使用SpEL, 从上下文中获取唯一标识
     * @return Spring-EL expression
     */
    String expression() default "";

    /**
     * 控制时长 默认：1 有效期要大于处理需要的时间，默认2分钟
     *
     * @return 标记时长，默认 2 min
     */
    int duration() default 2 * 60;

    /**
     * 时间单位 默认：s
     *
     * @return TimeUnit
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

    /**
     * 提示信息，可自定义
     *
     * @return String
     */
    String message() default "Repeat Request";

    /**
     * 是否在业务完成后立即清除幂等 key
     * @return boolean true: 立刻清除 false: 不处理
     */
    boolean removeKeyWhenFinished() default false;

    /**
     * 是否在业务执行异常时立刻清除幂等 key
     * @return boolean true: 立刻清除 false: 不处理
     */
    boolean removeKeyWhenError() default false;
}
