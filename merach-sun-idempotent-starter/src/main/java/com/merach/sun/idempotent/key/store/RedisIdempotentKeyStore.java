//package key.store;
//
//import lombok.RequiredArgsConstructor;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.data.redis.core.ValueOperations;
//
//import java.util.concurrent.TimeUnit;
//@RequiredArgsConstructor
//public class RedisIdempotentKeyStore implements IdempotentKeyStore {
//    private final StringRedisTemplate stringRedisTemplate;
//    @Override
//    public Boolean saveIfAbsent(String key, long duration, TimeUnit timeUnit) {
//        ValueOperations<String, String> opsForValue = stringRedisTemplate.opsForValue();
//        Boolean saveSuccess = opsForValue.setIfAbsent(key, String.valueOf(System.currentTimeMillis()), duration,
//                timeUnit);
//        return saveSuccess != null && saveSuccess;
//    }
//
//    @Override
//    public void remove(String key) {
//        stringRedisTemplate.delete(key);
//    }
//}
