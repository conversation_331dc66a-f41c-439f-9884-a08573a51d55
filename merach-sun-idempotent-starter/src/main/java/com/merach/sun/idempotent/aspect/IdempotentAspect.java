package com.merach.sun.idempotent.aspect;

import com.merach.sun.idempotent.annotation.Idempotent;
import com.merach.sun.idempotent.exception.IdempotentException;
import com.merach.sun.idempotent.key.generator.IdempotentKeyGenerator;
import com.merach.sun.idempotent.key.store.IdempotentKeyStore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

@Aspect
@RequiredArgsConstructor
@Slf4j
public class IdempotentAspect {
    private final IdempotentKeyStore idempotentKeyStore;

    private final IdempotentKeyGenerator idempotentKeyGenerator;

    @Around("@annotation(idempotentAnnotation)")
    public Object around(ProceedingJoinPoint joinPoint, Idempotent idempotentAnnotation) throws Throwable {
        String idempotentKey = idempotentKeyGenerator.generate(joinPoint, idempotentAnnotation);
        Boolean hasSuccess = idempotentKeyStore.saveIfAbsent(idempotentKey, idempotentAnnotation.duration(), idempotentAnnotation.timeUnit());
        if (Boolean.FALSE.equals(hasSuccess)) {
            throw new IdempotentException("400", idempotentAnnotation.message());
        }

        try {
            Object result = joinPoint.proceed();
            if (idempotentAnnotation.removeKeyWhenFinished()) {
                idempotentKeyStore.remove(idempotentKey);
            }
            return result;
        } catch (Throwable e) {
            if (idempotentAnnotation.removeKeyWhenError()) {
                idempotentKeyStore.remove(idempotentKey);
            }
            throw e;
        }
    }
}
