package com.merach.sun.idempotent.key.generator;

import com.merach.sun.idempotent.annotation.Idempotent;
import com.merach.sun.common.utils.SpelUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;

@Slf4j
public class SpelIdempotentKeyGenerator implements IdempotentKeyGenerator {
    @Override
    public String generate(JoinPoint joinPoint, Idempotent idempotentAnnotation) {
        String uniqueExpression = idempotentAnnotation.expression();
        if ("".equals(uniqueExpression)) {
            return idempotentAnnotation.prefix();
        }


        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] args = joinPoint.getArgs();

        StandardEvaluationContext spelContext = SpelUtils.getSpelContext(joinPoint.getTarget(), method, args);

        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        if (requestAttributes != null) {
            spelContext.setVariable(RequestAttributes.REFERENCE_REQUEST, requestAttributes.getRequest());
        }
        String uniqueStr = SpelUtils.parseValueToString(spelContext, uniqueExpression);
        return idempotentAnnotation.prefix() + ":" + uniqueStr;
    }
}
