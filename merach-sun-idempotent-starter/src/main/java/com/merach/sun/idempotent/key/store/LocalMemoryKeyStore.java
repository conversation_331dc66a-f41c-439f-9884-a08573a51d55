package com.merach.sun.idempotent.key.store;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;

import java.util.concurrent.TimeUnit;

public class LocalMemoryKeyStore implements IdempotentKeyStore {
    private final TimedCache<String, Long> cache;

    public LocalMemoryKeyStore() {
        this.cache = CacheUtil.newTimedCache(Integer.MAX_VALUE);
        cache.schedulePrune(1);
    }

    @Override
    public Boolean saveIfAbsent(String key, long duration, TimeUnit timeUnit) {
        Long value = cache.get(key, false);
        if (value == null) {
            long timeOut = TimeUnit.MILLISECONDS.convert(duration, timeUnit);
            cache.put(key, System.currentTimeMillis(), timeOut);
            return true;
        }
        return false;
    }

    @Override
    public void remove(String key) {
        cache.remove(key);
    }
}
