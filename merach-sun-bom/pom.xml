<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>merach-sun-parent</artifactId>
        <groupId>com.merach.sun</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>merach-sun-bom</artifactId>
    <packaging>pom</packaging>

    <properties>
        <spring-boot.version>2.7.0</spring-boot.version>
        <spring-cloud.version>2021.0.8</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
        <redisson-spring.version>3.17.4</redisson-spring.version>
        <mybatis-plus.version>3.5.2</mybatis-plus.version>
        <rocketmq-spring-boot-starter.version>2.3.0</rocketmq-spring-boot-starter.version>
        <spring-rabbit.version>2.4.7</spring-rabbit.version>
    </properties>

    <dependencyManagement>
        <dependencies>

            <!-- 基础组件 -->
            <dependency>
                <groupId>com.merach.sun</groupId>
                <artifactId>merach-sun-common</artifactId>
                <version>${project.parent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.merach.sun</groupId>
                <artifactId>merach-sun-common-starter</artifactId>
                <version>${project.parent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.merach.sun</groupId>
                <artifactId>merach-sun-biz-starter</artifactId>
                <version>${project.parent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.merach.sun</groupId>
                <artifactId>merach-sun-cache-starter</artifactId>
                <version>${project.parent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.merach.sun</groupId>
                <artifactId>merach-sun-redis-lock-starter</artifactId>
                <version>${project.parent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.merach.sun</groupId>
                <artifactId>merach-sun-etcd-lock-starter</artifactId>
                <version>${project.parent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.merach.sun</groupId>
                <artifactId>merach-sun-feign-starter</artifactId>
                <version>${project.parent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.merach.sun</groupId>
                <artifactId>merach-sun-idempotent-starter</artifactId>
                <version>${project.parent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.merach.sun</groupId>
                <artifactId>merach-sun-log-starter</artifactId>
                <version>${project.parent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.merach.sun</groupId>
                <artifactId>merach-sun-rocketmq-starter</artifactId>
                <version>${project.parent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.merach.sun</groupId>
                <artifactId>merach-sun-snowflake-starter</artifactId>
                <version>${project.parent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.merach.sun</groupId>
                <artifactId>merach-sun-loadbalancer-starter</artifactId>
                <version>${project.parent.version}</version>
            </dependency>

            <!-- springboot -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- spring-cloud -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- spring-cloud-alibaba -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- redisson -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson-spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-data-27</artifactId>
                <version>${redisson-spring.version}</version>
            </dependency>

            <!-- mybatis-plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- rocketmq -->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq-spring-boot-starter.version}</version>
            </dependency>

            <!-- rabbitmq -->
            <dependency>
                <groupId>org.springframework.amqp</groupId>
                <artifactId>spring-rabbit</artifactId>
                <version>${spring-rabbit.version}</version>
            </dependency>

            <!-- tools -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.34</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>3.15.0</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>33.1.0-jre</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.13.3</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.14.0</version>
            </dependency>

            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.4</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.16.1</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>

            <dependency>
                <groupId>commons-digester</groupId>
                <artifactId>commons-digester</artifactId>
                <version>2.1</version>
            </dependency>

            <!-- fastjson todo 下掉 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>2.0.6</version>
            </dependency>

            <!-- hutool todo 下掉 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>5.8.10</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.10</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.2.1</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>4.1.2</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>4.1.2</version>
            </dependency>

            <dependency>
                <groupId>one.util</groupId>
                <artifactId>streamex</artifactId>
                <version>0.8.1</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.schedulerx</groupId>
                <artifactId>schedulerx2-spring-boot-starter</artifactId>
                <version>1.8.4</version>
                <!--如果用的是logback，需要把log4j和log4j2排除掉 -->
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>QLExpress</artifactId>
                <version>3.3.1</version>
            </dependency>

            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-easysdk</artifactId>
                <version>2.2.2</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>4.5.20</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-iot</artifactId>
                <version>7.41.0</version>
            </dependency>

            <!-- amqp 1.0 qpid client -->
            <dependency>
                <groupId>org.apache.qpid</groupId>
                <artifactId>qpid-jms-client</artifactId>
                <version>0.57.0</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-vod</artifactId>
                <version>2.15.12</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>vod20170321</artifactId>
                <version>2.16.16</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-live</artifactId>
                <version>3.9.7</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-sts</artifactId>
                <version>3.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.11.2</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibaba-dingtalk-service-sdk</artifactId>
                <version>2.0.0</version>
            </dependency>

            <dependency>
                <groupId>io.github.engagelab-mt</groupId>
                <artifactId>engagelab-sdk-java</artifactId>
                <version>0.0.2</version>
            </dependency>


            <!-- 微信支付 -->
            <dependency>
                <groupId>com.github.wechatpay-apiv3</groupId>
                <artifactId>wechatpay-java</artifactId>
                <version>0.2.4</version>
            </dependency>
            <!-- 支付宝支付 -->
            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-sdk-java</artifactId>
                <version>4.35.45.ALL</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.lindorm</groupId>
                <artifactId>lindorm-tsdb-client</artifactId>
                <version>1.0.2</version>
            </dependency>

            <!-- Etcd Client -->
            <dependency>
                <groupId>io.etcd</groupId>
                <artifactId>jetcd-core</artifactId>
                <version>0.8.5</version>
            </dependency>


            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>3.1.8</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.12.0</version>
            </dependency>

            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bom</artifactId>
                <version>2.24.13</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.github.yitter</groupId>
                <artifactId>yitter-idgenerator</artifactId>
                <version>1.0.6</version>
            </dependency>

            <!-- test -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.codemonstur</groupId>
                <artifactId>embedded-redis</artifactId>
                <version>1.4.2</version>
            </dependency>
            <!-- 嵌入式 MySQL (MariaDB4j) -->
            <dependency>
                <groupId>ch.vorburger.mariaDB4j</groupId>
                <artifactId>mariaDB4j</artifactId>
                <version>2.5.3</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


</project>