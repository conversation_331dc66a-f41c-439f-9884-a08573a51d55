package com.merach.sun.biz.infrastructure.autoconfigure;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.merach.sun.biz.infrastructure.handler.MybatisPlusMetaObjectHandler;
import com.merach.sun.biz.infrastructure.properties.BizProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@SpringBootConfiguration(proxyBeanMethods = false)
@EnableConfigurationProperties(BizProperties.class)
public class MybatisAutoConfiguration {

    private final BizProperties properties;

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty({"sun.biz.max-limit"})
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        log.info("=== MybatisPlusInterceptor bean enable ===");
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor(properties.getDbType());
        paginationInnerInterceptor.setMaxLimit(properties.getMaxLimit());
        interceptor.addInnerInterceptor(paginationInnerInterceptor);
        return interceptor;
    }

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = BizProperties.PREFIX, name = "enable-fill", havingValue = "true", matchIfMissing = true)
    public MybatisPlusMetaObjectHandler mybatisPlusMetaObjectHandler() {
        log.info("=== MybatisPlusMetaObjectHandler bean enable ===");
        return new MybatisPlusMetaObjectHandler();
    }

}
