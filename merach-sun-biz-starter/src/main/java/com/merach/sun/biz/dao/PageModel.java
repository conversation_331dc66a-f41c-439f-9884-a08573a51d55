package com.merach.sun.biz.dao;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/2/23 10:33
 */
@JsonIgnoreProperties(value = {"orders", "optimizeCountSql", "searchCount", "countId", "maxLimit", "optimizeJoinOfCountSql"})
@ToString
public class PageModel<T> extends com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO<T> {

    public PageModel() {
    }

    public PageModel(long current, long size) {
        this(current, size, 0L);
    }

    public PageModel(long current, long size, long total) {
        this(current, size, total, true);
    }

    public PageModel(long current, long size, boolean searchCount) {
        this(current, size, 0L, searchCount);
    }

    public PageModel(long current, long size, long total, boolean searchCount) {
        super(current, size, total, searchCount);
    }

    public static <T> PageModel<T> of(long current, long size) {
        return of(current, size, 0L);
    }

    public static <T> PageModel<T> of(long current, long size, long total) {
        return of(current, size, total, true);
    }

    public static <T> PageModel<T> of(long current, long size, boolean searchCount) {
        return of(current, size, 0L, searchCount);
    }

    public static <T> PageModel<T> of(long current, long size, long total, boolean searchCount) {
        return new PageModel<>(current, size, total, searchCount);
    }

}
