package com.merach.sun.biz.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.ReflectionKit;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.merach.sun.biz.dao.BaseWrapper;
import com.merach.sun.biz.dao.PageModel;
import com.merach.sun.biz.dao.SortModel;
import com.merach.sun.biz.enums.ConditionEnum;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
public class BaseServiceImpl<M extends BaseMapper<T>, T> extends ServiceImpl<M, T> implements IBaseService<T> {

    private static final String ARG1 = "arg1";

    private static final String ARG2 = "arg2";

    private static final String EMPTY = "";

    private static final String ID = "id";

    private static final char UNDERLINE = '_';

    /**
     * 判断是否存在
     *
     * @param baseWrapper 查询条件
     * @return boolean
     * <AUTHOR>
     * @date 2022/2/22 11:18
     */
    @Override
    public boolean isExist(BaseWrapper<T> baseWrapper) {
        return count(baseWrapper) > 0;
    }

    /**
     * 判断是否存在
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           查询值
     * @return boolean
     * <AUTHOR>
     * @date 2022/2/22 11:18
     */
    @Override
    public boolean isExist(String column, ConditionEnum conditionEnum, Object val) {
        BaseWrapper<T> baseWrapper = this.addCondition(column, conditionEnum, val);
        return count(baseWrapper) > 0;
    }

    /**
     * 查询单个对象
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           查询值
     * @return T
     * <AUTHOR>
     * @date 2022/2/22 11:19
     */
    @Override
    public T getOne(String column, ConditionEnum conditionEnum, Object val) {
        BaseWrapper<T> baseWrapper = this.addCondition(column, conditionEnum, val);
        return getOne(baseWrapper);
    }

    /**
     * 查询单个对象
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           查询值
     * @param columns       查询指定字段
     * @return T
     * <AUTHOR>
     * @date 2022/2/22 11:19
     */
    @Override
    public T getOne(String column, ConditionEnum conditionEnum, Object val, String... columns) {
        BaseWrapper<T> baseWrapper = this.addCondition(column, conditionEnum, val);
        baseWrapper.select(columns);
        return getOne(baseWrapper);
    }

    /**
     * 查询集合
     *
     * @param sort  排序规则
     * @param sorts 排序字段
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2022/2/22 11:21
     */
    @Override
    public List<T> list(ConditionEnum sort, String... sorts) {
        BaseWrapper<T> baseWrapper = new BaseWrapper<>();
        if (sort == ConditionEnum.ASC) {
            baseWrapper.orderByAsc(Arrays.asList(sorts));
        } else if (sort == ConditionEnum.DESC) {
            baseWrapper.orderByDesc(Arrays.asList(sorts));
        }
        return list(baseWrapper);
    }

    /**
     * 查询集合
     *
     * @param sortModels 排序规则集合
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2022/2/22 11:22
     */
    @Override
    public List<T> list(List<SortModel> sortModels) {
        BaseWrapper<T> baseWrapper = new BaseWrapper<>();
        this.sort(baseWrapper, sortModels);
        return list(baseWrapper);
    }

    /**
     * 查询集合
     *
     * @param columns 查询指定字段
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2022/2/22 11:30
     */
    @Override
    public List<T> list(String... columns) {
        BaseWrapper<T> baseWrapper = new BaseWrapper<>();
        baseWrapper.select(columns);
        return list(baseWrapper);
    }

    /**
     * 查询集合
     *
     * @param sortModel 排序规则
     * @param columns   查询指定字段
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2022/2/22 11:35
     */
    @Override
    public List<T> list(SortModel sortModel, String... columns) {
        BaseWrapper<T> baseWrapper = new BaseWrapper<>();
        ConditionEnum conditionEnum = sortModel.getConditionEnum();
        if (conditionEnum == ConditionEnum.ASC) {
            baseWrapper.orderByAsc(Arrays.asList(sortModel.getColumns()));
        } else {
            baseWrapper.orderByDesc(Arrays.asList(sortModel.getColumns()));
        }
        baseWrapper.select(columns);

        return list(baseWrapper);
    }

    /**
     * 查询集合
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           查询值
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2022/2/22 11:36
     */
    @Override
    public List<T> list(String column, ConditionEnum conditionEnum, Object val) {
        BaseWrapper<T> baseWrapper = this.addCondition(column, conditionEnum, val);
        return list(baseWrapper);
    }

    /**
     * 查询集合
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           查询值
     * @param columns       查询指定字段
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2022/2/23 10:46
     */
    @Override
    public List<T> list(String column, ConditionEnum conditionEnum, Object val, String... columns) {
        BaseWrapper<T> baseWrapper = this.addCondition(column, conditionEnum, val);
        baseWrapper.select(columns);
        return list(baseWrapper);
    }

    /**
     * 查询集合
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           查询值
     * @param sortModel     排序规则
     * @param columns       查询指定字段
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2022/2/23 10:48
     */
    @Override
    public List<T> list(String column, ConditionEnum conditionEnum, Object val, SortModel sortModel, String... columns) {
        BaseWrapper<T> baseWrapper = this.addCondition(column, conditionEnum, val);
        baseWrapper.select(columns);
        this.sort(baseWrapper, Collections.singletonList(sortModel));
        return list(baseWrapper);
    }

    /**
     * 查询集合
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           查询值
     * @param sortModels    排序规则集合
     * @param columns       查询指定字段
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2022/2/23 13:43
     */
    @Override
    public List<T> list(String column, ConditionEnum conditionEnum, Object val, List<SortModel> sortModels, String... columns) {
        BaseWrapper<T> baseWrapper = this.addCondition(column, conditionEnum, val);
        baseWrapper.select(columns);
        this.sort(baseWrapper, sortModels);
        return list(baseWrapper);
    }

    /**
     * 查询集合
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           查询值
     * @param sort          排序规则
     * @param sorts         指定排序字段
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2022/2/23 13:44
     */
    @Override
    public List<T> list(String column, ConditionEnum conditionEnum, Object val, ConditionEnum sort, String... sorts) {
        BaseWrapper<T> baseWrapper = this.addCondition(column, conditionEnum, val);
        if (sort == ConditionEnum.ASC) {
            baseWrapper.orderByAsc(Arrays.asList(sorts));
        } else if (sort == ConditionEnum.DESC) {
            baseWrapper.orderByDesc(Arrays.asList(sorts));
        }
        return list(baseWrapper);
    }

    /**
     * 获取总条数
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           查询值
     * @return long
     * <AUTHOR>
     * @date 2022/2/23 13:46
     */
    @Override
    public long count(String column, ConditionEnum conditionEnum, Object val) {
        BaseWrapper<T> baseWrapper = this.addCondition(column, conditionEnum, val);
        return count(baseWrapper);
    }

    /**
     * 验证是否唯一
     *
     * @param id     ID
     * @param column 验证字段名
     * @param val    值
     * @return boolean
     * <AUTHOR>
     * @date 2022/2/23 13:46
     */
    @Override
    public boolean isOnly(Serializable id, String column, Object val) {
        return this.isOnly(id, ID, column, val);
    }

    /**
     * 验证是否唯一
     *
     * @param id      ID
     * @param idFiled ID字段名
     * @param column  验证字段名
     * @param val     值
     * @return boolean
     * <AUTHOR>
     * @date 2022/2/23 13:48
     */
    @Override
    public boolean isOnly(Serializable id, String idFiled, String column, Object val) {
        long count = this.count(column, ConditionEnum.EQ, val);
        if (id == null) {
            return count <= 0;
        } else {
            if (count > 1) {
                return false;
            }
        }

        if (count <= 0) {
            return true;
        }

        T one = this.getOne(idFiled, ConditionEnum.EQ, id, column);
        if (one == null) {
            return true;
        }

        Object value = ReflectionKit.getFieldValue(one, this.underlineToCamel(column));

        return ObjectUtil.equal(val, value);
    }

    /**
     * 验证是否唯一
     *
     * @param baseWrapper 查询条件
     * @param id          ID
     * @param val         值
     * @param columns     验证字段
     * @return boolean
     * <AUTHOR>
     * @date 2022/2/23 13:50
     */
    @Override
    public boolean isOnly(BaseWrapper<T> baseWrapper, Serializable id, Object val, String... columns) {
        long count = this.count(baseWrapper);
        if (val == null) {
            return count <= 0;
        } else {
            if (count > 1) {
                return false;
            }
        }

        if (count <= 0) {
            return true;
        }

        T one = this.getOne(baseWrapper);
        if (one == null) {
            return true;
        }

        boolean isOnly = true;
        for (String column : columns) {
            if (id != null) {
                Object id1 = ReflectionKit.getFieldValue(one, this.underlineToCamel(ID));
                if (!ObjectUtil.equal(id, id1)) {
                    isOnly = false;
                    break;
                }
            }

            Object value1 = ReflectionKit.getFieldValue(val, this.underlineToCamel(column));
            Object value2 = ReflectionKit.getFieldValue(one, this.underlineToCamel(column));
            if (!ObjectUtil.equal(value1, value2)) {
                isOnly = false;
                break;
            }
        }

        return isOnly;
    }

    /**
     * 删除
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           值
     * @return boolean
     * <AUTHOR>
     * @date 2022/2/23 13:51
     */
    @Override
    public boolean remove(String column, ConditionEnum conditionEnum, Object val) {
        BaseWrapper<T> baseWrapper = this.addCondition(column, conditionEnum, val);
        return remove(baseWrapper);
    }

    /**
     * 分页查询
     *
     * @param current       页码
     * @param size          显示条数
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           值
     * @return com.baomidou.mybatisplus.core.metadata.IPage<T>
     * <AUTHOR>
     * @date 2022/2/23 13:54
     */
    @Override
    public IPage<T> page(long current, long size, String column, ConditionEnum conditionEnum, Object val) {
        BaseWrapper<T> baseWrapper = this.addCondition(column, conditionEnum, val);
        return page(PageModel.of(current, size), baseWrapper);
    }

    /**
     * 分页查询
     *
     * @param current 页码
     * @param size    显示条数
     * @param sort    排序规则
     * @param sorts   排序字段
     * @return com.baomidou.mybatisplus.core.metadata.IPage<T>
     * <AUTHOR>
     * @date 2022/2/23 13:56
     */
    @Override
    public IPage<T> page(long current, long size, ConditionEnum sort, String... sorts) {
        PageModel<T> page = PageModel.of(current, size);
        List<OrderItem> orderItems;
        if (sort == ConditionEnum.ASC) {
            orderItems = OrderItem.ascs(sorts);
        } else {
            orderItems = OrderItem.descs(sorts);
        }
        page.addOrder(orderItems);

        return page(page);
    }

    /**
     * 分页查询
     *
     * @param current 页码
     * @param size    显示条数
     * @param sorts   排序字段
     * @return com.baomidou.mybatisplus.core.metadata.IPage<T>
     * <AUTHOR>
     * @date 2022/2/23 13:56
     */
    @Override
    public IPage<T> page(long current, long size, SortModel... sorts) {
        PageModel<T> page = PageModel.of(current, size);
        List<OrderItem> orderItems = new ArrayList<>();
        Stream.of(sorts).forEach(sortModel -> orderItems.addAll(sortModel.getConditionEnum() == ConditionEnum.ASC ? OrderItem.ascs(sortModel.getColumns()) : OrderItem.descs(sortModel.getColumns())));
        page.addOrder(orderItems);
        return page(page);
    }

    /**
     * 分页查询
     *
     * @param current       页码
     * @param size          显示条数
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           值
     * @param columns       指定查询字段名
     * @return com.baomidou.mybatisplus.core.metadata.IPage<T>
     * <AUTHOR>
     * @date 2022/2/23 13:57
     */
    @Override
    public IPage<T> page(long current, long size, String column, ConditionEnum conditionEnum, Object val, String... columns) {
        BaseWrapper<T> baseWrapper = this.addCondition(column, conditionEnum, val);
        baseWrapper.select(columns);
        return page(PageModel.of(current, size), baseWrapper);
    }

    /**
     * 分页查询
     *
     * @param current       页码
     * @param size          显示条数
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           值
     * @param sort          排序规则
     * @param sorts         指定排序字段
     * @return com.baomidou.mybatisplus.core.metadata.IPage<T>
     * <AUTHOR>
     * @date 2022/2/23 13:58
     */
    @Override
    public IPage<T> page(long current, long size, String column, ConditionEnum conditionEnum, Object val, ConditionEnum sort, String... sorts) {
        BaseWrapper<T> baseWrapper = this.addCondition(column, conditionEnum, val);
        if (sort == ConditionEnum.ASC) {
            baseWrapper.orderByAsc(Arrays.asList(sorts));
        } else if (sort == ConditionEnum.DESC) {
            baseWrapper.orderByDesc(Arrays.asList(sorts));
        }
        return page(PageModel.of(current, size), baseWrapper);
    }

    /**
     * 分页查询
     *
     * @param current       页码
     * @param size          显示条数
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           值
     * @param sortModel     排序规则
     * @param columns       指定查询字段
     * @return com.baomidou.mybatisplus.core.metadata.IPage<T>
     * <AUTHOR>
     * @date 2022/2/23 14:00
     */
    @Override
    public IPage<T> page(long current, long size, String column, ConditionEnum conditionEnum, Object val, SortModel sortModel, String... columns) {
        BaseWrapper<T> baseWrapper = this.addCondition(column, conditionEnum, val);
        baseWrapper.select(columns);

        ConditionEnum sort = sortModel.getConditionEnum();
        String[] sorts = sortModel.getColumns();
        if (sort == ConditionEnum.ASC) {
            baseWrapper.orderByAsc(Arrays.asList(sorts));
        } else if (sort == ConditionEnum.DESC) {
            baseWrapper.orderByDesc(Arrays.asList(sorts));
        }
        return page(PageModel.of(current, size), baseWrapper);
    }

    /**
     * 分页查询
     *
     * @param current       页码
     * @param size          显示条数
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           值
     * @param sortModels    排序规则集合
     * @param columns       指定查询字段
     * @return com.baomidou.mybatisplus.core.metadata.IPage<T>
     * <AUTHOR>
     * @date 2022/2/23 14:01
     */
    @Override
    public IPage<T> page(long current, long size, String column, ConditionEnum conditionEnum, Object val, List<SortModel> sortModels, String... columns) {
        BaseWrapper<T> baseWrapper = this.addCondition(column, conditionEnum, val);
        baseWrapper.select(columns);
        this.sort(baseWrapper, sortModels);
        return page(PageModel.of(current, size), baseWrapper);
    }

    /**
     * 分页查询
     *
     * @param current       页码
     * @param size          显示条数
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           值
     * @param sortModels    排序规则集合
     * @return com.baomidou.mybatisplus.core.metadata.IPage<T>
     * <AUTHOR>
     * @date 2022/2/23 14:03
     */
    @Override
    public IPage<T> page(long current, long size, String column, ConditionEnum conditionEnum, Object val, List<SortModel> sortModels) {
        BaseWrapper<T> baseWrapper = this.addCondition(column, conditionEnum, val);
        this.sort(baseWrapper, sortModels);
        return page(PageModel.of(current, size), baseWrapper);
    }

    /**
     * 分页查询
     *
     * @param current     页码
     * @param size        显示条数
     * @param baseWrapper 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<T>
     * <AUTHOR>
     * @date 2022/2/23 14:03
     */
    @Override
    public IPage<T> page(long current, long size, BaseWrapper<T> baseWrapper) {
        return this.page(PageModel.of(current, size), baseWrapper);
    }

    /**
     * 分页查询
     *
     * @param current 页码
     * @param size    显示条数
     * @return com.baomidou.mybatisplus.core.metadata.IPage<java.util.Map < java.lang.String, java.lang.Object>>
     * <AUTHOR>
     * @date 2022/2/23 14:06
     */
    @Override
    public IPage<Map<String, Object>> pageMaps(long current, long size) {
        Page<Map<String, Object>> page = PageModel.of(current, size);
        return pageMaps(page);
    }

    /**
     * 分页查询
     *
     * @param current     页码
     * @param size        显示条数
     * @param baseWrapper 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<java.util.Map < java.lang.String, java.lang.Object>>
     * <AUTHOR>
     * @date 2022/2/23 14:06
     */
    @Override
    public IPage<Map<String, Object>> pageMaps(long current, long size, BaseWrapper<T> baseWrapper) {
        Page<Map<String, Object>> page = PageModel.of(current, size);
        return pageMaps(page, baseWrapper);
    }

    /**
     * 设置排序规则
     *
     * @param baseQuery  查询条件
     * @param sortModels 排序规则集合
     * <AUTHOR>
     * @date 2022/2/22 11:33
     */
    private void sort(BaseWrapper<T> baseQuery, List<SortModel> sortModels) {
        if (CollUtil.isNotEmpty(sortModels)) {
            sortModels.forEach(sortModel -> {
                ConditionEnum sort = sortModel.getConditionEnum();
                String[] sorts = sortModel.getColumns();

                if (sort == ConditionEnum.ASC) {
                    baseQuery.orderByAsc(Arrays.asList(sorts));
                } else if (sort == ConditionEnum.DESC) {
                    baseQuery.orderByDesc(Arrays.asList(sorts));
                }
            });
        }
    }

    /**
     * 添加查询条件
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           查询值
     * @return com.merach.sun.biz.dao.BaseQuery<T>
     * <AUTHOR>
     * @date 2022/2/22 11:33
     */
    private BaseWrapper<T> addCondition(String column, ConditionEnum conditionEnum, Object val) {
        BaseWrapper<T> query = new BaseWrapper<>();
        switch (conditionEnum) {
            case EQ:
                query.eq(column, val);
                break;
            case NE:
                query.ne(column, val);
                break;
            case GT:
                query.gt(column, val);
                break;
            case GE:
                query.ge(column, val);
                break;
            case LT:
                query.lt(column, val);
                break;
            case LE:
                query.le(column, val);
                break;
            case LIKE_ANY:
                query.like(column, val);
                break;
            case LIKE_LEFT:
                query.likeLeft(column, val);
                break;
            case LIKE_RIGHT:
                query.likeRight(column, val);
                break;
            case NOT_LIKE_ANY:
                query.notLike(column, val);
                break;
            case IS_NULL:
                query.isNull(column);
                break;
            case IS_BLANK:
                query.apply("(" + column + " IS NULL OR " + column + " = '')");
                break;
            case IS_NOT_NULL:
                query.isNotNull(column);
                break;
            case IS_NOT_BLANK:
                query.isNotNull(column).ne(column, EMPTY);
                break;
            case APPLY:
                query.apply(column, val);
                break;
            case BETWEEN: {
                Map<String, Object> objectMap = BeanUtil.beanToMap(val);
                query.between(column, objectMap.get(ARG1), objectMap.get(ARG2));
                break;
            }
            case NOT_BETWEEN: {
                Map<String, Object> map = BeanUtil.beanToMap(val);
                query.notBetween(column, map.get(ARG1), map.get(ARG2));
                break;
            }
            case IN: {
                Collection<?> inColl = (Collection<?>) val;
                query.in(column, inColl);
                break;
            }
            case NOT_IN: {
                Collection<?> coll = (Collection<?>) val;
                query.notIn(column, coll);
                break;
            }
        }

        return query;
    }

    /**
     * 下划线转驼峰
     *
     * @param param 参数
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/2/22 11:34
     */
    private String underlineToCamel(String param) {
        if (param == null) {
            return EMPTY;
        }

        String temp = param.toLowerCase();
        int len = temp.length();
        StringBuilder sb = new StringBuilder(len);
        for (int i = 0; i < len; i++) {
            char c = temp.charAt(i);
            if (c == UNDERLINE) {
                if (++i < len) {
                    sb.append(Character.toUpperCase(temp.charAt(i)));
                }
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }

}
