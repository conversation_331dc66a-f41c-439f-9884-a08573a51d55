package com.merach.sun.biz.infrastructure.properties;

import com.baomidou.mybatisplus.annotation.DbType;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = BizProperties.PREFIX)
public class BizProperties {

    public static final String PREFIX = "sun.biz";

    /**
     * 分页最大显示条数，默认：100
     */
    private long maxLimit = 100L;

    /**
     * 是否开启自动填充，默认：true
     */
    private Boolean enableFill = true;

    /**
     * 数据库类型
     */
    private DbType dbType = DbType.MYSQL;

}
