package com.merach.sun.biz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.merach.sun.biz.dao.BaseWrapper;
import com.merach.sun.biz.dao.SortModel;
import com.merach.sun.biz.enums.ConditionEnum;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IBaseService<T> extends IService<T> {

    /**
     * 判断是否存在
     *
     * @param baseWrapper 查询条件
     * @return boolean
     * <AUTHOR>
     * @date 2022/2/22 11:18
     */
    boolean isExist(BaseWrapper<T> baseWrapper);

    /**
     * 判断是否存在
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           查询值
     * @return boolean
     * <AUTHOR>
     * @date 2022/2/22 11:18
     */
    boolean isExist(String column, ConditionEnum conditionEnum, Object val);

    /**
     * 查询单个对象
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           查询值
     * @return T
     * <AUTHOR>
     * @date 2022/2/22 11:19
     */
    T getOne(String column, ConditionEnum conditionEnum, Object val);

    /**
     * 查询单个对象
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           查询值
     * @param columns       查询指定字段
     * @return T
     * <AUTHOR>
     * @date 2022/2/22 11:19
     */
    T getOne(String column, ConditionEnum conditionEnum, Object val, String... columns);

    /**
     * 查询集合
     *
     * @param sort  排序规则
     * @param sorts 排序字段
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2022/2/22 11:21
     */
    List<T> list(ConditionEnum sort, String... sorts);

    /**
     * 查询集合
     *
     * @param sortModels 排序规则集合
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2022/2/22 11:22
     */
    List<T> list(List<SortModel> sortModels);

    /**
     * 查询集合
     *
     * @param columns 查询指定字段
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2022/2/22 11:30
     */
    List<T> list(String... columns);

    /**
     * 查询集合
     *
     * @param sortModel 排序规则
     * @param columns   查询指定字段
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2022/2/22 11:35
     */
    List<T> list(SortModel sortModel, String... columns);

    /**
     * 查询集合
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           查询值
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2022/2/22 11:36
     */
    List<T> list(String column, ConditionEnum conditionEnum, Object val);

    /**
     * 查询集合
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           查询值
     * @param columns       查询指定字段
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2022/2/23 10:46
     */
    List<T> list(String column, ConditionEnum conditionEnum, Object val, String... columns);

    /**
     * 查询集合
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           查询值
     * @param sortModel     排序规则
     * @param columns       查询指定字段
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2022/2/23 10:48
     */
    List<T> list(String column, ConditionEnum conditionEnum, Object val, SortModel sortModel, String... columns);

    /**
     * 查询集合
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           查询值
     * @param sortModels    排序规则集合
     * @param columns       查询指定字段
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2022/2/23 13:43
     */
    List<T> list(String column, ConditionEnum conditionEnum, Object val, List<SortModel> sortModels, String... columns);

    /**
     * 查询集合
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           查询值
     * @param sort          排序规则
     * @param sorts         指定排序字段
     * @return java.util.List<T>
     * <AUTHOR>
     * @date 2022/2/23 13:44
     */
    List<T> list(String column, ConditionEnum conditionEnum, Object val, ConditionEnum sort, String... sorts);

    /**
     * 获取总条数
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           查询值
     * @return long
     * <AUTHOR>
     * @date 2022/2/23 13:46
     */
    long count(String column, ConditionEnum conditionEnum, Object val);

    /**
     * 验证是否唯一
     *
     * @param id     ID
     * @param column 验证字段名
     * @param val    值
     * @return boolean
     * <AUTHOR>
     * @date 2022/2/23 13:46
     */
    boolean isOnly(Serializable id, String column, Object val);

    /**
     * 验证是否唯一
     *
     * @param id      ID
     * @param idFiled ID字段名
     * @param column  验证字段名
     * @param val     值
     * @return boolean
     * <AUTHOR>
     * @date 2022/2/23 13:48
     */
    boolean isOnly(Serializable id, String idFiled, String column, Object val);

    /**
     * 验证是否唯一
     *
     * @param baseWrapper 查询条件
     * @param id          ID
     * @param val         值
     * @param columns     验证字段
     * @return boolean
     * <AUTHOR>
     * @date 2022/2/23 13:50
     */
    boolean isOnly(BaseWrapper<T> baseWrapper, Serializable id, Object val, String... columns);

    /**
     * 删除
     *
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           值
     * @return boolean
     * <AUTHOR>
     * @date 2022/2/23 13:51
     */
    boolean remove(String column, ConditionEnum conditionEnum, Object val);

    /**
     * 分页查询
     *
     * @param current       页码
     * @param size          显示条数
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           值
     * @return com.baomidou.mybatisplus.core.metadata.IPage<T>
     * <AUTHOR>
     * @date 2022/2/23 13:54
     */
    IPage<T> page(long current, long size, String column, ConditionEnum conditionEnum, Object val);

    /**
     * 分页查询
     *
     * @param current 页码
     * @param size    显示条数
     * @param sort    排序规则
     * @param sorts   排序字段
     * @return com.baomidou.mybatisplus.core.metadata.IPage<T>
     * <AUTHOR>
     * @date 2022/2/23 13:56
     */
    IPage<T> page(long current, long size, ConditionEnum sort, String... sorts);

    /**
     * 分页查询
     *
     * @param current 页码
     * @param size    显示条数
     * @param sorts   排序字段
     * @return com.baomidou.mybatisplus.core.metadata.IPage<T>
     * <AUTHOR>
     * @date 2022/2/23 13:56
     */
    IPage<T> page(long current, long size, SortModel... sorts);

    /**
     * 分页查询
     *
     * @param current       页码
     * @param size          显示条数
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           值
     * @param columns       指定查询字段名
     * @return com.baomidou.mybatisplus.core.metadata.IPage<T>
     * <AUTHOR>
     * @date 2022/2/23 13:57
     */
    IPage<T> page(long current, long size, String column, ConditionEnum conditionEnum, Object val, String... columns);

    /**
     * 分页查询
     *
     * @param current       页码
     * @param size          显示条数
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           值
     * @param sort          排序规则
     * @param sorts         指定排序字段
     * @return com.baomidou.mybatisplus.core.metadata.IPage<T>
     * <AUTHOR>
     * @date 2022/2/23 13:58
     */
    IPage<T> page(long current, long size, String column, ConditionEnum conditionEnum, Object val, ConditionEnum sort, String... sorts);

    /**
     * 分页查询
     *
     * @param current       页码
     * @param size          显示条数
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           值
     * @param sortModel     排序规则
     * @param columns       指定查询字段
     * @return com.baomidou.mybatisplus.core.metadata.IPage<T>
     * <AUTHOR>
     * @date 2022/2/23 14:00
     */
    IPage<T> page(long current, long size, String column, ConditionEnum conditionEnum, Object val, SortModel sortModel, String... columns);

    /**
     * 分页查询
     *
     * @param current       页码
     * @param size          显示条数
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           值
     * @param sortModels    排序规则集合
     * @param columns       指定查询字段
     * @return com.baomidou.mybatisplus.core.metadata.IPage<T>
     * <AUTHOR>
     * @date 2022/2/23 14:01
     */
    IPage<T> page(long current, long size, String column, ConditionEnum conditionEnum, Object val, List<SortModel> sortModels, String... columns);

    /**
     * 分页查询
     *
     * @param current       页码
     * @param size          显示条数
     * @param column        数据库字段名
     * @param conditionEnum 查询条件
     * @param val           值
     * @param sortModels    排序规则集合
     * @return com.baomidou.mybatisplus.core.metadata.IPage<T>
     * <AUTHOR>
     * @date 2022/2/23 14:03
     */
    IPage<T> page(long current, long size, String column, ConditionEnum conditionEnum, Object val, List<SortModel> sortModels);

    /**
     * 分页查询
     *
     * @param current     页码
     * @param size        显示条数
     * @param baseWrapper 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<T>
     * <AUTHOR>
     * @date 2022/2/23 14:03
     */
    IPage<T> page(long current, long size, BaseWrapper<T> baseWrapper);

    /**
     * 分页查询
     *
     * @param current 页码
     * @param size    显示条数
     * @return com.baomidou.mybatisplus.core.metadata.IPage<java.util.Map < java.lang.String, java.lang.Object>>
     * <AUTHOR>
     * @date 2022/2/23 14:06
     */
    IPage<Map<String, Object>> pageMaps(long current, long size);

    /**
     * 分页查询
     *
     * @param current     页码
     * @param size        显示条数
     * @param baseWrapper 查询条件
     * @return com.baomidou.mybatisplus.core.metadata.IPage<java.util.Map < java.lang.String, java.lang.Object>>
     * <AUTHOR>
     * @date 2022/2/23 14:06
     */
    IPage<Map<String, Object>> pageMaps(long current, long size, BaseWrapper<T> baseWrapper);

}

