package com.merach.sun.biz.dao;

import com.merach.sun.biz.enums.ConditionEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
public class SortModel{

    private ConditionEnum conditionEnum;
    private String[] columns;
    public static SortModel asc(String... columns) {
        return new SortModel(ConditionEnum.ASC, columns);
    }

    public static SortModel desc(String... columns) {
        return new SortModel(ConditionEnum.DESC, columns);
    }

}
