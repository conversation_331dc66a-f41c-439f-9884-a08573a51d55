package com.merach.sun.snowflake.properties;

import com.merach.sun.snowflake.core.Method;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

@Getter
@Setter
@ConfigurationProperties(prefix = SnowflakeProperties.PREFIX)
public class SnowflakeProperties {

    public static final String PREFIX = "sun.snowflake";

    /**
     * 机器码位长，默认：9
     */
    private byte workerIdBitLength = 9;

    /**
     * 序列数位长，默认：6。如果每秒请求数不超过5W，保持默认值6即可；
     * 如果超过5W，不超过50W，建议赋值10或更大，以此类推。
     * 规则要求：WorkerIdBitLength + SeqBitLength 不超过 22。
     */
    private byte seqBitLength = 6;

    /**
     * 雪花算法，默认：漂移算法
     */
    private Method snowflakeMethod = Method.DRIFT;

    /**
     * 注册机器ID重试次数，默认：3次
     */
    private int retryNum = 3;

    /**
     * 机器ID过期时间，默认：2小时
     */
    private Duration workerIdTimeout = Duration.ofHours(2L);

    /**
     * 初始延迟时间，默认：30
     */
    private long initialDelay = 30;

    /**
     * 每次执行任务的间隔时间，默认：60
     */
    private long delay = 60;

    /**
     * 时间单位，默认：分钟
     */
    private TimeUnit unit = TimeUnit.MINUTES;

    /**
     * 注册机器ID缓存库
     */
    private Integer redisDb;

}
