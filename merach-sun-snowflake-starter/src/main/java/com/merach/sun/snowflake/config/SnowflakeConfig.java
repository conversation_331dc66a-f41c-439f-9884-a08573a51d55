package com.merach.sun.snowflake.config;

import com.merach.sun.snowflake.core.Snowflake;
import com.merach.sun.snowflake.properties.SnowflakeProperties;
import lombok.RequiredArgsConstructor;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

@RequiredArgsConstructor
@EnableConfigurationProperties(SnowflakeProperties.class)
@SpringBootConfiguration(proxyBeanMethods = false)
public class SnowflakeConfig {

    private final SnowflakeProperties snowflakeProperties;

    private final RedisProperties redisProperties;

    @Bean
    public Snowflake snowflake(RedisConnectionFactory redisConnectionFactory) {
        StringRedisTemplate stringRedisTemplate;
        if (snowflakeProperties.getRedisDb() == null) {
            stringRedisTemplate = new StringRedisTemplate(redisConnectionFactory);
        } else {
            Assert.isTrue(snowflakeProperties.getRedisDb() >= 0, "redisDb must >= 0");

            RedisStandaloneConfiguration standaloneConfiguration = this.getRedisStandaloneConfiguration(snowflakeProperties.getRedisDb());
            LettucePoolingClientConfiguration clientConfiguration = this.getLettucePoolingClientConfiguration();

            LettuceConnectionFactory factory = new LettuceConnectionFactory(standaloneConfiguration, clientConfiguration);
            factory.setDatabase(snowflakeProperties.getRedisDb());
            factory.afterPropertiesSet();
            stringRedisTemplate = new StringRedisTemplate(factory);
        }

        return new Snowflake(snowflakeProperties, stringRedisTemplate);
    }

    @SuppressWarnings("rawtypes")
    public LettucePoolingClientConfiguration getLettucePoolingClientConfiguration() {
        RedisProperties.Pool pool = redisProperties.getLettuce().getPool();
        GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
        poolConfig.setMaxIdle(pool.getMaxIdle());
        poolConfig.setMaxTotal(pool.getMaxActive());
        poolConfig.setMinIdle(pool.getMinIdle());
        poolConfig.setMaxWait(pool.getMaxWait());
        poolConfig.setTimeBetweenEvictionRuns(pool.getTimeBetweenEvictionRuns());

        return LettucePoolingClientConfiguration.builder().poolConfig(poolConfig).build();
    }

    private RedisStandaloneConfiguration getRedisStandaloneConfiguration(int database) {
        RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration();
        redisStandaloneConfiguration.setDatabase(database);
        redisStandaloneConfiguration.setHostName(redisProperties.getHost());
        redisStandaloneConfiguration.setPort(redisProperties.getPort());
        if (StringUtils.hasText(redisProperties.getPassword())) {
            redisStandaloneConfiguration.setPassword(RedisPassword.of(redisProperties.getPassword()));
        }
        if (StringUtils.hasText(redisProperties.getUsername())) {
            redisStandaloneConfiguration.setUsername(redisProperties.getUsername());
        }
        return redisStandaloneConfiguration;
    }

}
