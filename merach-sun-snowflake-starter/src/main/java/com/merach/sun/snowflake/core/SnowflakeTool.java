package com.merach.sun.snowflake.core;

import com.github.yitter.contract.ISnowWorker;
import com.merach.sun.snowflake.exception.SnowflakeException;

public final class SnowflakeTool {

    private static volatile ISnowWorker SNOW_WORKER;

    public static void setSnowWorker(ISnowWorker snowWorker) {
        SNOW_WORKER = snowWorker;
    }

    public static long getId() {
        if (SNOW_WORKER == null) {
            throw new SnowflakeException("SnowflakeTool idGenerator is not init!");
        }

        return SNOW_WORKER.nextId();
    }

    public static String getIdStr() {
        return Long.toString(getId());
    }

}
