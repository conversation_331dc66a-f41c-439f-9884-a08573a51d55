package com.merach.sun.snowflake.core;

import com.github.yitter.contract.ISnowWorker;
import com.github.yitter.contract.IdGeneratorOptions;
import com.github.yitter.core.SnowWorkerM1;
import com.github.yitter.core.SnowWorkerM2;
import com.merach.sun.snowflake.exception.SnowflakeException;
import com.merach.sun.snowflake.properties.SnowflakeProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.Assert;

import javax.annotation.PreDestroy;
import java.time.Duration;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public class Snowflake {

    /**
     * 获取机器ID锁
     */
    private static final String SNOWFLAKE_WORKER_ID_LOCK = "snowflake_worker_id_lock";

    /**
     * 获取机器ID锁
     */
    private static final String SNOWFLAKE_WORKER_ID_KEY = "snowflake_worker_id:%s";

    /**
     * 基础时间戳：2023-02-20 18:00:00
     */
    private static final long BASE_TIME = 1676887200000L;

    /**
     * 重试计数器
     */
    private static final AtomicInteger RETRY_NUM = new AtomicInteger(0);

    private static volatile String WORKER_ID_KEY;

    private static volatile String WORKER_ID;

    private final SnowflakeProperties snowflakeProperties;

    private final StringRedisTemplate stringRedisTemplate;

    private final ISnowWorker snowWorker;

    public Snowflake(SnowflakeProperties snowflakeProperties, StringRedisTemplate stringRedisTemplate) {
        Assert.isTrue(snowflakeProperties.getRetryNum() >= 0, "retryNum must >= 0");
        Assert.isTrue(snowflakeProperties.getWorkerIdBitLength() > 0, "workerIdBitLength must > 0");
        Assert.isTrue(snowflakeProperties.getSeqBitLength() > 0, "seqBitLength must > 0");

        this.snowflakeProperties = snowflakeProperties;
        this.stringRedisTemplate = stringRedisTemplate;
        this.snowWorker = this.getSnowWorker();

        SnowflakeTool.setSnowWorker(this.snowWorker);
        this.updateWorkerIdTimeTask();
    }

    public long nextId() {
        return snowWorker.nextId();
    }

    @PreDestroy
    private void destroy() {
        log.info("recycling snowflake workerId: {}", WORKER_ID);
        if (WORKER_ID_KEY == null) {
            return;
        }

        this.stringRedisTemplate.delete(WORKER_ID_KEY);
    }

    private ISnowWorker getSnowWorker() {
        byte workerIdBitLength = snowflakeProperties.getWorkerIdBitLength();
        byte seqBitLength = snowflakeProperties.getSeqBitLength();

        IdGeneratorOptions idGeneratorOptions = new IdGeneratorOptions(this.getWorkerId(workerIdBitLength));
        idGeneratorOptions.BaseTime = BASE_TIME;
        idGeneratorOptions.WorkerIdBitLength = workerIdBitLength;
        idGeneratorOptions.SeqBitLength = seqBitLength;

        return this.snowflakeProperties.getSnowflakeMethod() == Method.DRIFT ? new SnowWorkerM1(idGeneratorOptions) :
                new SnowWorkerM2(idGeneratorOptions);
    }

    private short getWorkerId(int workerIdBitLength) {
        short workerId = 0;
        int maxWorkerId = (2 << (workerIdBitLength - 1)) - 1;
        this.tryLock();
        for (int i = 0; i <= maxWorkerId; i++) {
            String key = String.format(SNOWFLAKE_WORKER_ID_KEY, i);
            Boolean hasKey = stringRedisTemplate.hasKey(key);
            if (hasKey != null && hasKey) {
                if (log.isDebugEnabled()) {
                    log.debug("already exist snowflake workerId: {}", i);
                }

                continue;
            }

            WORKER_ID_KEY = key;
            WORKER_ID = Integer.toString(i);
            stringRedisTemplate.opsForValue().set(key, WORKER_ID, this.snowflakeProperties.getWorkerIdTimeout());
            workerId = (short) i;
            break;
        }

        this.delLock();
        return workerId;
    }

    private void tryLock() {
        if (RETRY_NUM.get() > snowflakeProperties.getRetryNum()) {
            throw new SnowflakeException("存在多台机器同时启动，注册机器ID失败，请尝试重新启动");
        }

        Boolean absent = stringRedisTemplate.opsForValue().setIfAbsent(SNOWFLAKE_WORKER_ID_LOCK, SNOWFLAKE_WORKER_ID_LOCK, Duration.ofMillis(500));
        if (absent == null || !absent) {
            try {
                log.warn("存在其他应用进程注册机器ID，触发重试");
                TimeUnit.MILLISECONDS.sleep(500L);
                RETRY_NUM.incrementAndGet();
            } catch (InterruptedException e) {
                log.error("注册机器ID异常，异常信息：", e);
            }

            this.tryLock();
        }
    }

    private void delLock() {
        stringRedisTemplate.delete(SNOWFLAKE_WORKER_ID_LOCK);
    }

    private void updateWorkerIdTimeTask() {
        ScheduledExecutorService service = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r);
            thread.setName("update-workerId-thread");
            thread.setDaemon(true);
            return thread;
        });

        service.scheduleWithFixedDelay(this::updateWorkerIdTime, snowflakeProperties.getInitialDelay(), snowflakeProperties.getDelay(),
                snowflakeProperties.getUnit());
    }

    private void updateWorkerIdTime() {
        if (WORKER_ID_KEY == null || WORKER_ID == null) {
            return;
        }

        this.stringRedisTemplate.opsForValue().set(WORKER_ID_KEY, WORKER_ID, snowflakeProperties.getWorkerIdTimeout());
    }

}
