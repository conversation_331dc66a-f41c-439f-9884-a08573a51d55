package com.merach.sun.log.config;

import com.merach.sun.log.aspect.RestControllerLogAspect;
import com.merach.sun.log.aspect.LoggableAspect;
import com.merach.sun.log.filter.TraceIdFilter;
import com.merach.sun.log.filter.HttpServletLoggingFilter;
import org.junit.jupiter.api.*;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.boot.web.servlet.FilterRegistrationBean;

import static org.assertj.core.api.Assertions.assertThat;

class LogConfigTest {

    private final ApplicationContextRunner contextRunner =
            new ApplicationContextRunner().withUserConfiguration(LogConfig.class);

    @Test
    void shouldLoadLogTagAspectByDefault() {
        contextRunner.run(context -> {
            assertThat(context).hasSingleBean(LoggableAspect.class);
        });
    }

    @Test
    void shouldLoadTraceIdFilterByDefault() {
        contextRunner.run(context -> {
            assertThat(context).hasSingleBean(FilterRegistrationBean.class);
            FilterRegistrationBean<?> filterBean = context.getBean(
                    FilterRegistrationBean.class
            );
            assertThat(filterBean.getFilter()).isInstanceOf(TraceIdFilter.class);
        });
    }

    @Test
    void shouldLoadControllerLogAspectWhenEnabled() {
        contextRunner
                .withPropertyValues("mrk.logging.web-access.aop.enable=true")
                .run(context -> {
                    assertThat(context).hasSingleBean(RestControllerLogAspect.class);
                });
    }

    @Test
    void shouldNotLoadControllerLogAspectWhenDisabled() {
        contextRunner
                .withPropertyValues("mrk.logging.web-access.aop.enable=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(RestControllerLogAspect.class);
                });
    }

    @Test
    void shouldLoadHttpServletLoggingFilterWhenEnabled() {
        contextRunner
                .withPropertyValues("mrk.logging.web-access.filter.enable=true")
                .run(context -> {
                    assertThat(
                            context.getBeansOfType(FilterRegistrationBean.class).values()
                    ).anyMatch(filterBean ->
                            filterBean.getFilter() instanceof HttpServletLoggingFilter
                    );
                });
    }

    @Test
    void shouldNotLoadHttpServletLoggingFilterWhenDisabled() {
        contextRunner
                .withPropertyValues("mrk.logging.web-access.filter.enable=false")
                .run(context -> {
                    assertThat(
                            context.getBeansOfType(FilterRegistrationBean.class).values()
                    ).noneMatch(filterBean ->
                            filterBean.getFilter() instanceof HttpServletLoggingFilter
                    );
                });
    }
}
