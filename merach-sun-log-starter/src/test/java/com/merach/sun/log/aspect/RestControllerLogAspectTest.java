package com.merach.sun.log.aspect;

import com.merach.sun.log.config.WebAccessLogProperties;
import org.aspectj.lang.ProceedingJoinPoint;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.*;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RestControllerLogAspectTest {

    @Mock
    private ProceedingJoinPoint joinPoint;

    private MockHttpServletRequest request;
    private MockHttpServletResponse response;
    private RestControllerLogAspect aspect;

    @BeforeEach
    void setUp() {
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        ServletRequestAttributes attributes = new ServletRequestAttributes(
                request,
                response
        );
        RequestContextHolder.setRequestAttributes(attributes);

        WebAccessLogProperties.AopProperties properties = new WebAccessLogProperties.AopProperties();
        properties.setEnabled(true);
        properties.setUrlPatterns(List.of("/api/**"));
        properties.setExcludeUrlPatterns(List.of("/api/exclude/**"));
        aspect = new RestControllerLogAspect(properties);
    }

    @Test
    void shouldProceedWithLoggingForNonExcludedPath() throws Throwable {
        // Given
        request.setMethod("GET");
        request.setRequestURI("/api/test");
        when(joinPoint.getArgs()).thenReturn(new Object[]{"test"});
        when(joinPoint.proceed()).thenReturn("result");

        // When
        Object result = aspect.restControllerLogAround(joinPoint);

        // Then
        assertThat(result).isEqualTo("result");
        verify(joinPoint, times(1)).proceed();
    }

    @Test
    void shouldSkipLoggingForExcludedPath() throws Throwable {
        // Given
        request.setMethod("GET");
        request.setRequestURI("/exclude/test");
        when(joinPoint.proceed()).thenReturn("result");

        // When
        Object result = aspect.restControllerLogAround(joinPoint);

        // Then
        assertThat(result).isEqualTo("result");
        verify(joinPoint, times(1)).proceed();
        verify(joinPoint, never()).getArgs();
    }

    @Test
    void shouldSkipLoggingForExcludedPath2() throws Throwable {
        // Given
        request.setMethod("GET");
        request.setRequestURI("/api/exclude/test");
        when(joinPoint.proceed()).thenReturn("result");

        // When
        Object result = aspect.restControllerLogAround(joinPoint);

        // Then
        assertThat(result).isEqualTo("result");
        verify(joinPoint, times(1)).proceed();
        verify(joinPoint, never()).getArgs();
    }

    @Test
    void shouldHandleNullResult() throws Throwable {
        // Given
        request.setMethod("GET");
        request.setRequestURI("/api/test");
        when(joinPoint.getArgs()).thenReturn(new Object[]{});
        when(joinPoint.proceed()).thenReturn(null);

        // When
        Object result = aspect.restControllerLogAround(joinPoint);

        // Then
        assertThat(result).isNull();
        verify(joinPoint, times(1)).proceed();
    }

    @Test
    void shouldLogAndRethrowException() throws Throwable {
        // Given
        request.setMethod("GET");
        request.setRequestURI("/api/test");
        RuntimeException exception = new RuntimeException("Test exception");
        when(joinPoint.getArgs()).thenReturn(new Object[]{});
        when(joinPoint.proceed()).thenThrow(exception);

        // When/Then
        assertThrows(RuntimeException.class, () -> {
            assertThat(aspect.restControllerLogAround(joinPoint)).isNull();
        });
    }
}
