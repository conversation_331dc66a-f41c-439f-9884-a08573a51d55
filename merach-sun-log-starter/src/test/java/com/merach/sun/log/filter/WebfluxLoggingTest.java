package com.merach.sun.log.filter;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import com.merach.sun.log.TestApplication;
import com.merach.sun.log.config.LogConfig;
import org.junit.jupiter.api.*;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.*;
import org.springframework.test.web.reactive.server.*;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@WebFluxTest(LoggingController.class)
@Import(LogConfig.class)
@ContextConfiguration(classes = TestApplication.class)
@TestPropertySource(properties = {
        "mrk.logging.web-access.webflux.enabled=true",
})
public class WebfluxLoggingTest {

    @Autowired
    private WebTestClient webTestClient;

    @Test
    void shouldLogIncomingMessage() {
        TestAppender appender = new TestAppender();
        Logger logger = (Logger) LoggerFactory.getLogger(LoggingController.class);
        logger.addAppender(appender);
        appender.start();

        String testMessage = "test";

        webTestClient.get()
                .uri(uriBuilder -> uriBuilder.path("/log")
                        .queryParam("message", testMessage)
                        .build())
                .exchange()
                .expectStatus().isOk()
                .expectBody(String.class).isEqualTo(testMessage.toUpperCase());

        // 验证日志
        List<ILoggingEvent> logs = appender.getEvents();
        assertThat(logs).anyMatch(event ->
                event.getFormattedMessage().contains("Received message: " + testMessage) &&
                        event.getLevel() == Level.INFO);

        appender.stop();
        logger.detachAppender(appender);
    }
}