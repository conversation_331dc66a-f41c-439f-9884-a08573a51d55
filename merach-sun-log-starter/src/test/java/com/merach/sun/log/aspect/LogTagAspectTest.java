package com.merach.sun.log.aspect;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

import com.merach.sun.log.annotation.LogTag;

import java.lang.reflect.Method;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class LogTagAspectTest {

    @Mock
    private ProceedingJoinPoint joinPoint;

    @Mock
    private LogTag logTag;

    private LoggableAspect aspect;

    @Mock
    private MethodSignature methodSignature;

    @BeforeEach
    void setUp() {
        aspect = new LoggableAspect();

        // Setup default method signature
        when(joinPoint.getSignature()).thenReturn(methodSignature);
        when(joinPoint.getTarget()).thenReturn(new DummyTarget());
        when(methodSignature.getMethod()).thenReturn(getDummyMethod());
    }

    private Method getDummyMethod() {
        try {
            return DummyTarget.class.getMethod("dummyMethod");
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
    }

    static class DummyTarget {

        public void dummyMethod() {
        }
    }

    @Test
    void shouldLogWithCustomTagValue() throws Throwable {
        // Given
        when(logTag.value()).thenReturn("CustomTag");
        when(joinPoint.getArgs()).thenReturn(new Object[]{"test"});
        when(joinPoint.proceed()).thenReturn("result");

        // When
        Object result = aspect.loggableAround(joinPoint, logTag);

        // Then
        assertThat(result).isEqualTo("result");
        verify(joinPoint, times(1)).proceed();
        verify(joinPoint, times(1)).getArgs();
    }

    @Test
    void shouldLogWithDefaultTagWhenValueIsEmpty() throws Throwable {
        // Given
        when(logTag.value()).thenReturn("");
        when(joinPoint.getArgs()).thenReturn(new Object[]{"test"});
        when(joinPoint.proceed()).thenReturn("result");

        // When
        Object result = aspect.loggableAround(joinPoint, logTag);

        // Then
        assertThat(result).isEqualTo("result");
        verify(joinPoint, times(1)).proceed();
        verify(joinPoint, times(1)).getArgs();
    }

    @Test
    void shouldHandleNullResult() throws Throwable {
        // Given
        when(logTag.value()).thenReturn("CustomTag");
        when(joinPoint.getArgs()).thenReturn(new Object[]{});
        when(joinPoint.proceed()).thenReturn(null);

        // When
        Object result = aspect.loggableAround(joinPoint, logTag);

        // Then
        assertThat(result).isNull();
        verify(joinPoint, times(1)).proceed();
    }

    @Test
    void shouldLogAndRethrowException() throws Throwable {
        // Given
        when(logTag.value()).thenReturn("CustomTag");
        RuntimeException exception = new RuntimeException("Test exception");
        when(joinPoint.getArgs()).thenReturn(new Object[]{});
        when(joinPoint.proceed()).thenThrow(exception);

        // When/Then
        try {
            aspect.loggableAround(joinPoint, logTag);
        } catch (Throwable e) {
            assertThat(e)
                    .isInstanceOf(RuntimeException.class)
                    .hasMessage("Test exception");
        }
    }
}
