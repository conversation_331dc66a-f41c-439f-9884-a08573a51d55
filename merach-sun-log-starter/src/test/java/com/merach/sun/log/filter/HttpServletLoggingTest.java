package com.merach.sun.log.filter;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import com.merach.sun.log.TestApplication;
import com.merach.sun.log.config.LogConfig;
import org.aspectj.lang.annotation.Before;
import org.junit.jupiter.api.*;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.*;
import org.springframework.test.web.servlet.*;
import org.springframework.test.web.servlet.setup.*;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest(classes = TestApplication.class)
@AutoConfigureMockMvc
@TestPropertySource(locations = "classpath:application-test.properties")
public class HttpServletLoggingTest {
    @Autowired
    private MockMvc mockMvc;

    @Test
    void shouldLogIncomingMessage() throws Exception {
        TestAppender appender = new TestAppender();
        Logger logger = (Logger) LoggerFactory.getLogger(LoggingController.class);
        logger.addAppender(appender);
        appender.start();

        String testMessage = "test";

        mockMvc.perform(get("/log2").param("message", testMessage))
                .andExpect(status().isOk())
                .andExpect(result -> assertThat(result.getResponse().getContentAsString()).isEqualTo(testMessage.toUpperCase()));

        // 验证日志
        List<ILoggingEvent> logs = appender.getEvents();
        assertThat(logs).anyMatch(event ->
                event.getFormattedMessage().contains("Received message: " + testMessage) &&
                        event.getLevel() == Level.INFO);

        appender.stop();
        logger.detachAppender(appender);
    }

    @Test
    void shouldLogIncomingMessage2() throws Exception {
        TestAppender appender = new TestAppender();
        Logger logger = (Logger) LoggerFactory.getLogger(LoggingController.class);
        logger.addAppender(appender);
        appender.start();

        String testMessage = "test";

        mockMvc.perform(get("/log3").content(testMessage))
                .andExpect(status().isOk())
                .andExpect(result -> assertThat(result.getResponse().getContentAsString()).isEqualTo(testMessage.toUpperCase()));

        // 验证日志
        List<ILoggingEvent> logs = appender.getEvents();
        assertThat(logs).anyMatch(event ->
                event.getFormattedMessage().contains("Received message: " + testMessage) &&
                        event.getLevel() == Level.INFO);

        appender.stop();
        logger.detachAppender(appender);
    }
}