package com.merach.sun.log.filter;

import com.merach.sun.log.config.WebAccessLogProperties.HttpFilterProperties;
import org.junit.jupiter.api.*;
import org.springframework.mock.web.MockFilterChain;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import java.io.IOException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

class HttpServletLoggingFilterTest {

    private HttpServletLoggingFilter filter;
    private MockHttpServletRequest request;
    private MockHttpServletResponse response;
    private FilterChain filterChain;

    @BeforeEach
    void setUp() {
        filter = new HttpServletLoggingFilter(new HttpFilterProperties());
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        filterChain = new MockFilterChain();
    }

    @Test
    void shouldWrapRequestAndResponse() throws ServletException, IOException {
        // Given
        request.setContent("test content".getBytes());
        request.setMethod("POST");
        request.setRequestURI("/api/test");

        // When
        filter.doFilter(request, response, filterChain);

        // Then
        MockFilterChain mockChain = (MockFilterChain) filterChain;
        assertThat(mockChain.getRequest()).isInstanceOf(
                ContentCachingRequestWrapper.class
        );
        assertThat(mockChain.getResponse()).isInstanceOf(
                ContentCachingResponseWrapper.class
        );
    }

    @Test
    void shouldPreserveResponseContent() throws ServletException, IOException {
        // Given
        String responseContent = "test response";
        FilterChain customChain = (req, res) -> {
            res.getWriter().write(responseContent);
        };

        // When
        filter.doFilter(request, response, customChain);

        // Then
        assertThat(response.getContentAsString()).isEqualTo(responseContent);
    }

    @Test
    void shouldHandleEmptyRequestAndResponse()
            throws ServletException, IOException {
        // When
        filter.doFilter(request, response, filterChain);

        // Then
        assertThat(response.getContentAsString()).isEmpty();
    }

    @Test
    void shouldHandleFilterChainException() throws ServletException, IOException {
        // Given
        RuntimeException exception = new RuntimeException("Test exception");
        FilterChain errorChain = (req, res) -> {
            throw exception;
        };

        // When/Then
        assertThrows(RuntimeException.class, () -> {
            filter.doFilter(request, response, errorChain);
        });
    }
}
