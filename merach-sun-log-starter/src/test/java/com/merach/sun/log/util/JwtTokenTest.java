package com.merach.sun.log.util;

import org.junit.jupiter.api.Test;

import java.util.Base64;

public class JwtTokenTest {

    @Test
    public void test1(){
        String token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc1RyYSI6IjAiLCJ2ZXJzaW9uTnVtIjoiNTIxMCIsInRlcm1pbmFsIjoiMiIsInVzZXJUeXBlIjoiMSIsImlkIjoiMTU5OTQzNzAxODkzMTA2MjgyOSIsImxvZ2luIjoiMSIsImVudiI6IjMwMSIsInR5cGUiOjEsImV4cCI6MTc1MzY2OTIzOSwidXNlcklkIjoiMTU5OTQzNzAxODkzMTA2MjgyOSIsInZlcnNpb24iOiI1LjIuMS4wIiwidXVpZCI6IjZiOWUwZTJlODQxZjQxNjliMTYxNzVhYjYzZTQzOTIxIn0.vxc2iqn42BImWOjWwvgwWRgWixCgzhp4baSE1NKnpJfSam4Y3ibbd6lbv-zsejxaL29sFkd9q2_NiFvhGEn0kw";
        String[] parts = token.split("\\.");
        String header = new String(Base64.getUrlDecoder().decode(parts[0]));
        String payload = new String(Base64.getUrlDecoder().decode(parts[1]));

        System.out.println("Header: " + header);
        System.out.println("Payload: " + payload);
    }
}
