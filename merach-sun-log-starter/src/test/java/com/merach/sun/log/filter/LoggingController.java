package com.merach.sun.log.filter;

import cn.hutool.core.io.IoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@RestController
@Slf4j
public class LoggingController {

    @GetMapping("/log2")
    public String logMessage2(@RequestParam String message) {
        log.info("Received message: {}", message);
        return message.toUpperCase();
    }

    @GetMapping("/log3")
    public String logMessage3(HttpServletRequest request) throws IOException {
        String read = IoUtil.read(request.getReader());
        log.info("Received message: {}", read);
        return read.toUpperCase();
    }

    @GetMapping("/log")
    public Mono<String> logMessage(@RequestParam String message) {
        return Mono.just(message)
                .doOnNext(msg -> log.info("Received message: {}", msg))
                .map(String::toUpperCase);
    }
}