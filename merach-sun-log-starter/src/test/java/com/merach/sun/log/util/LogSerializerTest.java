package com.merach.sun.log.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.merach.sun.log.serializer.LogSerializer;
import lombok.SneakyThrows;
import org.junit.jupiter.api.*;

import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

class LogSerializerTest {


    @Test
    void testShortStringNotTruncated() {
        String input = "Hello World";
        String result = LogSerializer.serialize(input);
        assertTrue(result.contains("Hello World"));
        assertFalse(result.contains(" ... "));
    }

    @Test
    void testLargeListIsTruncated() {
        List<String> list = new ArrayList<>();
        for (int i = 0; i < 500; i++) {
            list.add("Item-" + i);
        }

        String result = LogSerializer.serialize(list);
        System.out.println(result);
        assertTrue(result.contains("\"Item-0\""));
        assertTrue(result.contains("\"Item-4\""));
        assertTrue(result.contains("..."));
        assertTrue(result.contains("\"Item-498\""));
        assertTrue(result.contains("\"Item-499\""));
    }

    @Test
    void testSmallListNotTruncated() {
        List<String> list = Arrays.asList("a", "b", "c");
        String result = LogSerializer.serialize(list);
        assertEquals(3, countOccurrences(result, "a") + countOccurrences(result, "b") + countOccurrences(result, "c"));
    }

    @Test
    void testLocalDateTimeFormatted() {
        LocalDateTime now = LocalDateTime.now();
        String expected = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String result = LogSerializer.serialize(now);
        System.out.println(result);
        assertTrue(result.contains(expected));
    }

    @Test
    void testDateFormatted() {
        Date now = new Date();
        String expected = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(now);
        String result = LogSerializer.serialize(now);
        assertTrue(result.contains(expected));
    }

    @Test
    void testNestedMapStructure() {
        Map<String, Object> nested = new HashMap<>();
        nested.put("key1", "value1");
        nested.put("key2", Arrays.asList("a", "b", "c"));

        Map<String, Object> outer = new HashMap<>();
        outer.put("nested", nested);

        String result = LogSerializer.serialize(outer);
        assertTrue(result.contains("\"key1\""));
        assertTrue(result.contains("\"a\""));
        assertTrue(result.contains("\"b\""));
    }

    @Test
    void testUnserializableObjectToStringFallback() {
        class BadToString {
            @Override
            public String toString() {
                return "BadToString";
            }
        }

        String result = LogSerializer.serialize(new BadToString());
        System.out.println(result);
        assertTrue(result.contains("BadToString"));
    }


    // Helper method to count occurrences of substring
    private int countOccurrences(String str, String substr) {
        return (str.length() - str.replace(substr, "").length()) / substr.length();
    }

    @Test
    @SneakyThrows
    public void testLogger() {
        URL resourceDir = getClass().getClassLoader().getResource("json");
        Files.list(Paths.get(resourceDir.toURI()))
                .filter(path -> path.toString().endsWith(".json"))
                .forEach(path -> {
                    try {
                        System.out.println(LogSerializer.serialize(Files.readString(path)));
                        System.out.println("--------------------");
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                });
    }

    @Test
    public void testSerializeArray() throws JsonProcessingException {
        System.out.println(LogSerializer.serialize(new Object()));
        Optional<Optional<LocalDateTime>> now = Optional.of(Optional.of(LocalDateTime.now()));
        System.out.println(LogSerializer.serialize(now));
        System.out.println(LogSerializer.MAPPER.writeValueAsString(now));
        System.out.println(LogSerializer.MAPPER.writeValueAsString("test1"));
    }
}