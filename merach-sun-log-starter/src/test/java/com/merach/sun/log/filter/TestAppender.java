package com.merach.sun.log.filter;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.AppenderBase;

import java.util.ArrayList;
import java.util.List;

public class TestAppender extends AppenderBase<ILoggingEvent> {
    
    private final List<ILoggingEvent> events = new ArrayList<>();
    
    @Override
    protected void append(ILoggingEvent event) {
        events.add(event);
    }
    
    public List<ILoggingEvent> getEvents() {
        return new ArrayList<>(events);
    }
    
    public void clearEvents() {
        events.clear();
    }
}