<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="FILE_LOG_PATTERN"
              value="${logging.pattern.file:-%d{yyyy-MM-dd HH:mm:ss.SSS} %5p [%t] %logger{36} traceId:%X{traceId} - %msg%n}"/>
    <property name="LOG_PATH" value="${logging.file.path:-logs}"/>

    <appender name="WEB_ACCESS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/web_access.log</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/%d{yyyy-MM-dd}/web_access.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>3</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="ASYNC_WEB_ACCESS_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <neverBlock>true</neverBlock>
        <appender-ref ref="WEB_ACCESS_FILE"/>
    </appender>

    <root level="INFO">
        <appender-ref ref="ASYNC_WEB_ACCESS_FILE"/>
    </root>

</configuration>