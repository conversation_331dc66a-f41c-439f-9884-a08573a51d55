package com.merach.sun.log.aspect;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.merach.sun.log.annotation.OperationLog;
import com.merach.sun.log.model.LogOperation;
import com.merach.sun.log.util.WebUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/26 22:31
 */
@Slf4j
@Aspect
@Order()
public class OperationLogAspect {

    @AfterReturning(value = "@annotation(com.merach.sun.log.annotation.OperationLog)", returning = "result")
    public void operationLog(JoinPoint joinPoint, Object result) {
        LogOperation logOperation = this.getLogOperation(joinPoint);
        if (logOperation == null) {
            return;
        }

        String param = logOperation.getParam();
        String body = logOperation.getBody();
        logOperation.setParam(null).setBody(null);
        log.info("log operation info: {} param: {} body: {} result: {}", JSON.toJSONString(logOperation), param, body, JSON.toJSONString(result));
    }

    @AfterThrowing(value = "@annotation(com.merach.sun.log.annotation.OperationLog)", throwing = "error")
    public void operationThrow(JoinPoint joinPoint, Throwable error) throws Throwable {
        LogOperation logOperation = this.getLogOperation(joinPoint);
        if (logOperation == null) {
            return;
        }

        String param = logOperation.getParam();
        String body = logOperation.getBody();
        logOperation.setParam(null).setBody(null);
        log.error("log operation error: {} param: {} body: {} exception: {}", JSON.toJSONString(logOperation), param, body, ExceptionUtil.getRootCauseMessage(error));
        throw error;
    }

    private LogOperation getLogOperation(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        OperationLog annotation = signature.getMethod().getAnnotation(OperationLog.class);
        if (annotation == null) {
            log.info("=== 切面未获取到指定注解：OperationLog 信息，不进行任何操作 ===");
            return null;
        }

        HttpServletRequest request = WebUtils.getRequest();
        String uri = request.getRequestURI();
        String method = request.getMethod();
        String ip = WebUtils.getIp(request);
        String param = this.getParam(request.getParameterMap());
        String body = this.getBody(request.getContentType(), joinPoint.getArgs());
        String name = StrUtil.isNotBlank(annotation.value()) ? annotation.value() : annotation.name();
        String action = joinPoint.getTarget().getClass().getName() + "#" + signature.getName();
        return new LogOperation(name, method, action, uri, ip, param, body);
    }

    private String getParam(Map<String, String[]> parameterMap) {
        if (MapUtil.isEmpty(parameterMap)) {
            return null;
        }

        JSONObject dict = new JSONObject(parameterMap.size());
        parameterMap.forEach((k, v) -> dict.put(k, String.join(",", v)));
        return dict.toJSONString();
    }

    private String getBody(String contentType, Object[] args) {
        if (!StrUtil.startWith(contentType, MediaType.APPLICATION_JSON_VALUE) || ArrayUtil.isEmpty(args)) {
            return null;
        }

        return JSON.toJSONString(args[0]);
    }

}
