package com.merach.sun.log.util;

import org.slf4j.MDC;

import java.util.Map;

public class MdcUtil {
    public static Runnable wrap(Runnable runnable, Map<String, String> context) {
        return () -> {
            if (context != null) {
                MDC.setContextMap(context);
            }
            try {
                runnable.run();
            } finally {
                MDC.clear();
            }
        };
    }
}