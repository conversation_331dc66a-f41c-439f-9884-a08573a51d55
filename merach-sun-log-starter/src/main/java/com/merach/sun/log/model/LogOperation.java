package com.merach.sun.log.model;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 操作日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-26
 */
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
public class LogOperation {

    private static final long serialVersionUID = 1L;

    /**
     * 功能名称
     */
    private String name;

    /**
     * 请求方式
     */
    private String method;

    /**
     * 执行方法
     */
    private String action;

    /**
     * 请求地址
     */
    private String url;

    /**
     * 请求IP
     */
    private String ip;

    /**
     * 请求参数
     */
    private String param;

    /**
     * 请求body参数
     */
    private String body;

    public LogOperation(String name, String method, String action, String url, String ip, String param, String body) {
        this.name = name;
        this.method = method;
        this.action = action;
        this.url = url;
        this.ip = ip;
        this.param = param;
        this.body = body;
    }

}
