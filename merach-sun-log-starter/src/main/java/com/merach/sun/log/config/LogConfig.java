package com.merach.sun.log.config;

import com.merach.sun.log.annotation.OperationLog;
import com.merach.sun.log.aspect.LoggableAspect;
import com.merach.sun.log.aspect.OperationLogAspect;
import com.merach.sun.log.aspect.RestControllerLogAspect;
import com.merach.sun.log.filter.HttpServletLoggingFilter;
import com.merach.sun.log.filter.TraceIdFilter;
import com.merach.sun.log.filter.WebfluxLoggingFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

/**
 * <AUTHOR>
 * @date 2022/6/21 11:18
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(WebAccessLogProperties.class)
public class LogConfig {

    @ConditionalOnClass(OperationLog.class)
    @Bean
    public OperationLogAspect operationLogAspect() {
        log.info("=== OperationLog bean enable ===");
        return new OperationLogAspect();
    }

    @Bean
    @ConditionalOnProperty(name = "mrk.logging.trace.enabled", havingValue = "true")
    public FilterRegistrationBean<TraceIdFilter> traceIdFilter() {
        log.info("=== Enable traceIdFilter ===");
        FilterRegistrationBean<TraceIdFilter> bean = new FilterRegistrationBean<>();
        bean.setFilter(new TraceIdFilter());
        bean.addUrlPatterns("/*");
        bean.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return bean;
    }

    @Bean
    @Order(Ordered.HIGHEST_PRECEDENCE)
    public LoggableAspect logTagAspect() {
        log.info("=== Enable logTagAspect ===");
        return new LoggableAspect();
    }

    @Bean
    @Order(Ordered.HIGHEST_PRECEDENCE)
    @ConditionalOnProperty(name = "mrk.logging.web-access.aop.enabled", havingValue = "true")
    public RestControllerLogAspect controllerLogAspect(WebAccessLogProperties properties) {
        log.info("=== Enable controllerLogAspect ===");
        return new RestControllerLogAspect(properties.getAop());
    }

    @Bean
    @ConditionalOnProperty(name = "mrk.logging.web-access.servlet.enabled", havingValue = "true")
    public FilterRegistrationBean<HttpServletLoggingFilter> httpServletLoggingFilter(WebAccessLogProperties properties) {
        log.info("=== Enable httpServletLoggingFilter ===");
        FilterRegistrationBean<HttpServletLoggingFilter> bean = new FilterRegistrationBean<>();
        bean.setFilter(new HttpServletLoggingFilter(properties.getServlet()));
        bean.addUrlPatterns("*");
        bean.setOrder(Ordered.HIGHEST_PRECEDENCE + 1);
        return bean;
    }

    @Bean
    @ConditionalOnProperty(name = "mrk.logging.web-access.webflux.enabled", havingValue = "true")
    public WebfluxLoggingFilter webfluxLoggingFilter(WebAccessLogProperties properties) {
        log.info("=== Enable webfluxLoggingFilter ===");
        return new WebfluxLoggingFilter(properties.getWebflux());
    }

}
