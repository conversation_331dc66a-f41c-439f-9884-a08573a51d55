package com.merach.sun.log.util;

import lombok.experimental.UtilityClass;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.reflect.Method;

@UtilityClass
public class AspectUtils {

    public static String getCallName(ProceedingJoinPoint joinPoint) {
        if (joinPoint == null || joinPoint.getSignature() == null || joinPoint.getTarget() == null) {
            return "UnknownMethod";
        }
        Signature signature = joinPoint.getSignature();
        Class<?> aClass = joinPoint.getTarget().getClass();
        if (signature instanceof MethodSignature) {
            Method method = ((MethodSignature) signature).getMethod();
            return aClass.getSimpleName() + "." + method.getName();
        }
        return signature.getName();
    }
}
