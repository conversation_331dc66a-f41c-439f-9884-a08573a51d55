package com.merach.sun.log.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 通用日志标记, 自动打印请求参数和返回结果
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface LogTag {

    /**
     * 标签名, 日志打印前缀, 默认为方法名
     */
    String value() default "";

    /**
     * 最大输出长度
     * 限制单个字段的最大输出长度, 默认500
     */
    int maxLength() default 500;

    /**
     * 是否忽略结果
     * 忽略后不打印返回值
     */
    boolean skipResult() default false;

    /**
     * 耗时警告阈值, 单位毫秒
     */
    long warnOver() default 300;
}
