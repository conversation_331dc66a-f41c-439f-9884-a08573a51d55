package com.merach.sun.log.filter;

import org.reactivestreams.Publisher;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

public class CachedServerHttpResponseDecorator extends ServerHttpResponseDecorator {
    private final ServerWebExchange exchange;
    private final AtomicReference<String> cachedBody = new AtomicReference<>();
    private final AtomicLong contentLength = new AtomicLong();


    public CachedServerHttpResponseDecorator(ServerWebExchange exchange) {
        super(exchange.getResponse());
        this.exchange = exchange;
    }

    @Override
    public Mono<Void> writeWith(Publisher<? extends DataBuffer> body) {
        return super.writeWith(Flux.from(body).map(dataBuffer -> {
            // 缓存响应体内容
            byte[] bytes = new byte[dataBuffer.readableByteCount()];
            dataBuffer.read(bytes);
            DataBufferUtils.release(dataBuffer);
            String bodyContent = new String(bytes, StandardCharsets.UTF_8);
            cachedBody.set(bodyContent);
            contentLength.set(bytes.length);
            return exchange.getResponse().bufferFactory().wrap(bytes);
        }));
    }

    public String getCachedBody() {
        return cachedBody.get();
    }

    public long getContentLength() {
        return contentLength.get();
    }
}