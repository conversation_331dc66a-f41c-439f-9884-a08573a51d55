<?xml version="1.0" encoding="UTF-8"?>
<included>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <springProperty name="SERVER_NAME" source="spring.application.name" defaultValue="app"/>
    <springProperty name="FILE_LOG_PATTERN" source="logging.pattern.file"
                    defaultValue="%d{yyyy-MM-dd HH:mm:ss.SSS} %5p [%t] %-40.40logger{39} traceId:%X{EagleEye-TraceID} - %msg%n%wEx"/>
    <springProperty name="LOG_PATH" source="logging.file.path" defaultValue="logs"/>
    <springProperty name="LOG_FILE" source="logging.file.name" defaultValue="${LOG_PATH}/${SERVER_NAME}.log"/>

    <springProperty name="LOGBACK_ROLLINGPOLICY_FILE_NAME_PATTERN"
                    source="logging.logback.rollingpolicy.file-name-pattern"
                    defaultValue="${LOG_PATH}/${SERVER_NAME}.%d{yyyy-MM-dd}.%i.log"/>
    <springProperty name="LOGBACK_ROLLINGPOLICY_CLEAN_HISTORY_ON_START"
                    source="logging.logback.rollingpolicy.clean-history-on-start" defaultValue="false"/>
    <springProperty name="LOGBACK_ROLLINGPOLICY_MAX_FILE_SIZE" source="logging.logback.rollingpolicy.max-file-size"
                    defaultValue="50MB"/>
    <springProperty name="LOGBACK_ROLLINGPOLICY_MAX_HISTORY" source="logging.logback.rollingpolicy.max-history"
                    defaultValue="7"/>
    <springProperty name="LOGBACK_ROLLINGPOLICY_TOTAL_SIZE_CAP" source="logging.logback.rollingpolicy.total-size-cap"
                    defaultValue="5GB"/>

</included>