package com.merach.sun.log.filter;

import org.slf4j.MDC;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.UUID;

import static com.merach.sun.log.trace.Constants.EAGLE_EYE_TRACE_ID;
import static com.merach.sun.log.trace.Constants.TRACE_ID;
import static com.merach.sun.log.trace.Constants.X_TRACE_ID;

public class TraceIdFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        if (!(request instanceof HttpServletRequest)) {
            chain.doFilter(request, response);
            return;
        }

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        httpRequest.getHeader(X_TRACE_ID);

        try {
            // 从请求头中获取traceId
            String traceId = getHeader(request, X_TRACE_ID);
            // 如果traceId为空，则从MDC中获取
            if (traceId == null) {
                traceId = MDC.get(EAGLE_EYE_TRACE_ID);
            }
            if (traceId == null) {
                traceId = MDC.get(TRACE_ID);
            }
            if (traceId == null) {
                traceId = generateUniqueId();
            }

            MDC.put(EAGLE_EYE_TRACE_ID, traceId);
            MDC.put(TRACE_ID, traceId);

            if (response instanceof HttpServletResponse) {
                HttpServletResponse httpResponse = (HttpServletResponse) response;
                httpResponse.setHeader(X_TRACE_ID, traceId);
            }

            chain.doFilter(request, response);
        } finally {
            MDC.remove(EAGLE_EYE_TRACE_ID);
            MDC.remove(TRACE_ID);
        }
    }

    private static String getHeader(ServletRequest request, String name) {
        return ((HttpServletRequest) request).getHeader(name);
    }

    public static String generateUniqueId() {
        return UUID.randomUUID().toString();
    }
}