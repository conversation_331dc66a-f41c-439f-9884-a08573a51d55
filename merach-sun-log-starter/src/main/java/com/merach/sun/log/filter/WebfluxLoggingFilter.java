package com.merach.sun.log.filter;

import com.merach.sun.log.config.WebAccessLogProperties.HttpFilterProperties;
import com.merach.sun.log.logback.LogbackLoggerFactory;
import com.merach.sun.log.serializer.HttpDumps;
import com.merach.sun.log.util.WebUtils;
import org.slf4j.Logger;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

public class WebfluxLoggingFilter implements WebFilter, Ordered {
    private static final Logger log = new LogbackLoggerFactory().getLogger(WebfluxLoggingFilter.class);
    private final HttpFilterProperties properties;
    private final HttpDumps httpDumps;

    public WebfluxLoggingFilter(HttpFilterProperties properties) {
        if (properties == null) {
            properties = new HttpFilterProperties();
            properties.setEnabled(true);
        }

        this.properties = properties;
        this.httpDumps = new HttpDumps().skipResponse(properties.isSkipResponse())
                .skipBody(properties.isSkipBody())
                .skipHeader(properties.isSkipHeader());
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        String uri = exchange.getRequest().getPath().toString();

        if (properties.getUrlPatterns() != null && !WebUtils.isMatch(uri, properties.getUrlPatterns())) {
            log.debug("Skip uri={}", uri);
            return chain.filter(exchange);
        }

        if (properties.getExcludeUrlPatterns() != null && WebUtils.isMatch(uri, properties.getExcludeUrlPatterns())) {
            log.debug("Skip uri={}", uri);
            return chain.filter(exchange);
        }

        long startTime = System.currentTimeMillis();
        CachedServerHttpRequestDecorator decoratedRequest = new CachedServerHttpRequestDecorator(exchange);
        CachedServerHttpResponseDecorator decoratedResponse = new CachedServerHttpResponseDecorator(exchange);

        ServerWebExchange exchangeWrapper = exchange.mutate()
                .request(decoratedRequest)
                .response(decoratedResponse)
                .build();
        return chain.filter(exchangeWrapper)
                .then(Mono.fromRunnable(() -> {
                    long costTime = System.currentTimeMillis() - startTime;

                    ServerHttpRequest request = exchange.getRequest();
                    ServerHttpResponse response = exchange.getResponse();

                    String logContent = httpDumps.dumpString(request.getMethodValue(),
                            request.getURI().toString(),
                            request.getHeaders(),
                            request.getQueryParams(),
                            decoratedRequest.getCachedBody(),
                            response.getStatusCode() != null ? response.getStatusCode().value() : 0,
                            response.getHeaders(), decoratedResponse.getCachedBody(), costTime);
                    log.info(logContent);
                }));
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 1;
    }
}