package com.merach.sun.log.filter;

import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.atomic.AtomicReference;

public class CachedServerHttpRequestDecorator extends ServerHttpRequestDecorator {
    private final ServerWebExchange exchange;
    private final AtomicReference<String> cachedBody = new AtomicReference<>();

    public CachedServerHttpRequestDecorator(ServerWebExchange exchange) {
        super(exchange.getRequest());
        this.exchange = exchange;
    }

    @Override
    public Flux<DataBuffer> getBody() {
        return super.getBody().map(dataBuffer -> {
            // 缓存请求体内容
            byte[] bytes = new byte[dataBuffer.readableByteCount()];
            dataBuffer.read(bytes);
            DataBufferUtils.release(dataBuffer);
            String body = new String(bytes, StandardCharsets.UTF_8);
            cachedBody.set(body);
            return exchange.getResponse().bufferFactory().wrap(bytes);
        });
    }

    public String getCachedBody() {
        return cachedBody.get();
    }
}