package com.merach.sun.log.aspect;

import com.merach.sun.log.config.WebAccessLogProperties;
import com.merach.sun.log.config.WebAccessLogProperties.AopProperties;
import com.merach.sun.log.serializer.LogSerializer;
import com.merach.sun.log.util.WebUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 通用的 Controller log 切面
 */
@Slf4j
@Aspect
public class RestControllerLogAspect {

    private final AopProperties properties;

    public RestControllerLogAspect(WebAccessLogProperties.AopProperties properties) {
        this.properties = properties;
    }

    @Pointcut("within(@org.springframework.web.bind.annotation.RestController *)")
    public void restControllerMethods() {
    }

    @Around("restControllerMethods()")
    public Object restControllerLogAround(ProceedingJoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return joinPoint.proceed();
        }

        HttpServletRequest request = attributes.getRequest();
        HttpServletResponse response = attributes.getResponse();
        String uri = request.getRequestURI();

        // URL匹配
        if (properties.getUrlPatterns() != null && !WebUtils.isMatch(uri, properties.getUrlPatterns())) {
            log.debug("Skip uri={}", uri);
            return joinPoint.proceed();
        }

        if (properties.getExcludeUrlPatterns() != null && WebUtils.isMatch(request.getRequestURI(), properties.getExcludeUrlPatterns())) {
            log.debug("Skip uri={}", request.getRequestURI());
            return joinPoint.proceed();
        }

        return logAround(joinPoint, request, response);
    }

    private Object logAround(ProceedingJoinPoint joinPoint, HttpServletRequest request, HttpServletResponse response) throws Throwable {
        String tag = String.format("%s %s", request.getMethod(), request.getRequestURI());
        long startTime = System.currentTimeMillis();
        log.info("[WEB] Start {} params {}", tag, LogSerializer.serializeArray(joinPoint.getArgs(), properties.getMaxLength()));
        Object result = null;
        try {
            result = joinPoint.proceed();
            return result;
        } finally {
            long costTime = System.currentTimeMillis() - startTime;
            if (result == null || properties.isSkipResult()) {
                if (costTime > properties.getWarnOver()) {
                    log.warn("[WEB] End {} status={} {}ms", tag, getResponseStatus(response), costTime);
                } else {
                    log.info("[WEB] End {} status={} {}ms", tag, getResponseStatus(response), costTime);
                }
            } else {
                if (costTime > properties.getWarnOver()) {
                    log.warn("[WEB] End {} status={} result {} {}ms", tag, getResponseStatus(response),
                            LogSerializer.serialize(result, properties.getMaxLength()), costTime);
                } else {
                    log.info("[WEB] End {} status={} result {} {}ms", tag, getResponseStatus(response),
                            LogSerializer.serialize(result, properties.getMaxLength()), costTime);
                }
            }
        }
    }

    private String getResponseStatus(HttpServletResponse response) {
        if (response == null) {
            return "";
        }
        return response.getStatus() + "";
    }
}
