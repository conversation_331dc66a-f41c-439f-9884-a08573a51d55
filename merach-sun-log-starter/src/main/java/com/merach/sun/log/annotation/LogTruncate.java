package com.merach.sun.log.annotation;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.merach.sun.log.serializer.LogTruncateSerializer;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 打印日志时对字段进行截取
 * 避免打印的字段过长
 * 仅对log-starter自动添加的日志有效
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@JacksonAnnotationsInside
@JsonSerialize(using = LogTruncateSerializer.class)
public @interface LogTruncate {
    int value() default 20; // 默认截断前n个字符
}