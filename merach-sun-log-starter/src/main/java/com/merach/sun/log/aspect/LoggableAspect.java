package com.merach.sun.log.aspect;

import com.merach.sun.log.annotation.LogTag;
import com.merach.sun.log.serializer.LogSerializer;
import com.merach.sun.log.util.AspectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;


/**
 * logTag 切面
 */
@Slf4j
@Aspect
public class LoggableAspect {

    @Around("@annotation(annotation)")
    public Object loggableAround(ProceedingJoinPoint joinPoint, LogTag annotation) throws Throwable {
        String tag = StringUtils.defaultIfBlank(annotation.value(), getMethodName(joinPoint));
        long startTime = System.currentTimeMillis();
        log.info("Start {} params {}", tag, LogSerializer.serializeArray(joinPoint.getArgs(), annotation.maxLength()));
        Object result = null;
        try {
            result = joinPoint.proceed();
            return result;
        } finally {
            long costTime = System.currentTimeMillis() - startTime;
            if (result == null || annotation.skipResult()) {
                if (costTime > annotation.warnOver()) {
                    log.warn("End {} {}ms", tag, costTime);
                } else {
                    log.info("End {} {}ms", tag, costTime);
                }
            } else {
                if (costTime > annotation.warnOver()) {
                    log.warn("End {} result {} {}ms", tag, LogSerializer.serialize(result, annotation.maxLength()), costTime);
                } else {
                    log.info("End {} result {} {}ms", tag, LogSerializer.serialize(result, annotation.maxLength()), costTime);
                }
            }
        }
    }

    private String getMethodName(ProceedingJoinPoint joinPoint) {
        return AspectUtils.getCallName(joinPoint);
    }
}
