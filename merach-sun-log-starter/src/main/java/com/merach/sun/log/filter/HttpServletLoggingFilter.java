package com.merach.sun.log.filter;

import com.merach.sun.log.config.WebAccessLogProperties.HttpFilterProperties;
import com.merach.sun.log.logback.LogbackLoggerFactory;
import com.merach.sun.log.serializer.HttpDumps;
import com.merach.sun.log.util.WebUtils;
import org.slf4j.Logger;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class HttpServletLoggingFilter extends OncePerRequestFilter {
    private static final Logger log = new LogbackLoggerFactory().getLogger(HttpServletLoggingFilter.class);

    private final HttpFilterProperties properties;
    private final HttpDumps httpDumps;

    public HttpServletLoggingFilter(HttpFilterProperties properties) {
        if (properties == null) {
            properties = new HttpFilterProperties();
            properties.setEnabled(true);
        }

        this.properties = properties;
        this.httpDumps = new HttpDumps().skipResponse(properties.isSkipResponse())
                .skipBody(properties.isSkipBody())
                .skipHeader(properties.isSkipHeader());
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String uri = request.getRequestURI();
        if (properties.getUrlPatterns() != null && !WebUtils.isMatch(uri, properties.getUrlPatterns())) {
            log.debug("Skip uri={}", uri);
            return;
        }

        if (properties.getExcludeUrlPatterns() != null && WebUtils.isMatch(uri, properties.getExcludeUrlPatterns())) {
            log.debug("Skip uri={}", uri);
            return;
        }

        long startTime = System.currentTimeMillis();
        // 包装Request和Response, 缓存Body数据
        ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(request);
        ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(response);

        try {
            filterChain.doFilter(requestWrapper, responseWrapper);
        } finally {
            long costTime = System.currentTimeMillis() - startTime;

            log.info(httpDumps.dumpString(requestWrapper, responseWrapper, costTime));
            // 必须调用此方法释放响应流
            responseWrapper.copyBodyToResponse();
        }
    }

}