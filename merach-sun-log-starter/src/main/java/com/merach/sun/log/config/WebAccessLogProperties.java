package com.merach.sun.log.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

@Data
@ConfigurationProperties(prefix = "mrk.logging.web-access")
public class WebAccessLogProperties {
    /**
     * aop日志配置
     */
    private AopProperties aop;
    /**
     * filter日志配置
     */
    private HttpFilterProperties servlet;

    /**
     * webflux日志配置
     */
    private HttpFilterProperties webflux;

    @Data
    public static class AopProperties {
        /**
         * 是否启用
         */
        private boolean enabled;

        /**
         * 请求路径匹配
         */
        private List<String> urlPatterns;

        /**
         * 忽略请求路径匹配
         */
        private List<String> excludeUrlPatterns;

        /**
         * 限制单个参数输出最大长度
         */
        private int maxLength;
        /**
         * 忽略输出返回值
         * 如果为true，则返回值不会打印
         */
        private boolean skipResult;

        /**
         * 耗时警告阈值, 单位毫秒
         */
        private long warnOver = 300;
    }

    @Data
    public static class HttpFilterProperties {
        /**
         * 是否启用
         */
        private boolean enabled;

        /**
         * 请求路径匹配
         */
        private List<String> urlPatterns;

        /**
         * 忽略请求路径匹配
         */
        private List<String> excludeUrlPatterns;

        /**
         * 是否忽略响应
         * 如果为true，则响应 header和 body 不会打印
         */
        private boolean skipResponse;

        /**
         * 是否忽略http-body
         * 如果为true，则请求体和响应体 不会打印
         */
        private boolean skipBody;

        /**
         * 是否忽略http头
         * 如果为true，则请求头和响应头 不会打印
         */
        private boolean skipHeader;
    }
}