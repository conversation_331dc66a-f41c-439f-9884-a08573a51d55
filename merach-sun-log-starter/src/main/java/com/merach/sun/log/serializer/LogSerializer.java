package com.merach.sun.log.serializer;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.ZonedDateTimeSerializer;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.stream.Collectors;

@Slf4j
public class LogSerializer {

    public static final ObjectMapper MAPPER = getObjectMapper();
    public static final String DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final int MAX_OUTPUT_LENGTH = 500;

    private static ObjectMapper getObjectMapper() {
        return JsonMapper.builder()
                .serializationInclusion(JsonInclude.Include.NON_NULL)
                .addModule(new Jdk8Module())
                .addModule(getJavaTimeModule())
                .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                .disable(MapperFeature.SORT_PROPERTIES_ALPHABETICALLY)
                .build();
    }

    private static JavaTimeModule getJavaTimeModule() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_PATTERN);
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(formatter));
        javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(formatter));
        javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(formatter));
        javaTimeModule.addSerializer(ZonedDateTime.class, new ZonedDateTimeSerializer(formatter));
        return javaTimeModule;
    }

    public static String serializeArray(Object[] args, int maxLength) {
        if (args == null) {
            return "";
        }
        return Arrays.stream(args).map(x -> serialize(x, maxLength)).collect(Collectors.joining(", "));
    }

    public static String serialize(Object obj, int maxLength) {
        return truncate(serializeJson(obj), maxLength > 0 ? maxLength : MAX_OUTPUT_LENGTH);
    }

    public static String serialize(Object obj) {
        return serialize(obj, MAX_OUTPUT_LENGTH);
    }

    private static String serializeJson(Object obj) {
        if (obj == null) {
            return null;
        }

        if (obj instanceof String) {
            return (String) obj;
        }

        if (isSimpleType(obj)) {
            return obj.toString();
        }

        if (obj instanceof Date) {
            return new SimpleDateFormat(DATE_PATTERN).format((Date) obj);
        }

        if (isIOType(obj)) {
            return "";
        }

        try {
            return MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            return obj.toString();
        }
    }

    @SuppressWarnings("all")
    private static boolean isIOType(Object x) {
        return (x instanceof javax.servlet.ServletRequest
                || x instanceof javax.servlet.http.HttpServletRequest
                || x instanceof javax.servlet.ServletResponse
                || x instanceof javax.servlet.http.HttpServletResponse
                || x instanceof java.io.InputStream
                || x instanceof java.io.OutputStream
                || x instanceof java.io.Reader
                || x instanceof java.io.Writer
                || x instanceof java.io.Closeable);
    }

    private static boolean isSimpleType(Object obj) {
        return obj instanceof Number || obj instanceof Boolean || obj instanceof Character;
    }

    public static String truncate(String s, int maxLength) {
        if (s == null || s.length() <= maxLength) {
            return s;
        }
        int head = maxLength / 2;
        int tail = maxLength / 2;

        return s.substring(0, head) + " ... " + s.substring(s.length() - tail);
    }
}