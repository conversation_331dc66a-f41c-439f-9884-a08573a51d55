package com.merach.sun.log.interceptor;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.slf4j.MDC;

import static com.merach.sun.log.trace.Constants.TRACE_ID;
import static com.merach.sun.log.trace.Constants.X_TRACE_ID;

@Deprecated
public class FeignTraceInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate template) {
        // 从 MDC 中获取 traceId
        String traceId = MDC.get(TRACE_ID);
        if (traceId != null) {
            template.header(X_TRACE_ID, traceId);
        }
    }
}