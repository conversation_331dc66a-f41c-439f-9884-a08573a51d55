package com.merach.sun.log.serializer;

import cn.hutool.core.collection.ListUtil;
import org.springframework.http.HttpHeaders;
import org.springframework.util.MultiValueMap;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


public class HttpDumps {
    private boolean skipResponse;
    private boolean skipBody;
    private boolean skipHeader;

    public String dumpString(ContentCachingRequestWrapper request, ContentCachingResponseWrapper response, long costTime) {
        return dumpString(request.getMethod(),
                request.getRequestURI(),
                getHeaders(request),
                toMultiValueMap(request.getParameterMap()),
                new String(request.getContentAsByteArray(), StandardCharsets.UTF_8),
                response.getStatus(),
                getHeaders(response), new String(response.getContentAsByteArray(), StandardCharsets.UTF_8), costTime);
    }

    public String dumpString(String method, String uri, HttpHeaders requestHeaders, MultiValueMap<String, String> parameters,
                             String requestBody, int statusCode, HttpHeaders responseHeaders,
                             String responseBody, long costTime) {
        StringBuilder buf = new StringBuilder();
        buf.append("\n").append(method).append(" ").append(uri).append(" ");
        buf.append("params: ").append(getParameterContent(parameters)).append("  ");
        buf.append("status: ").append(statusCode).append(" ").append(costTime).append("ms").append("\n");

        if (!skipHeader) {
            buf.append("===== request header =====").append("\n").append(getHeaderContent(requestHeaders)).append("\n");
        }
        if (!skipBody) {
            buf.append("===== request body =====").append("\n").append(requestBody == null ? "" : requestBody).append("\n");
        }

        if (skipResponse) {
            return buf.toString();
        }

        if (!skipHeader) {
            buf.append("===== response headers =====").append("\n").append(getHeaderContent(responseHeaders)).append("\n");
        }
        if (!skipBody) {
            buf.append("===== response body =====").append("\n").append(responseBody == null ? "" : responseBody);
        }
        return buf.toString();
    }

    private String getParameterContent(MultiValueMap<String, String> parameters) {
        return parameters.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + String.join(",", entry.getValue()))
                .collect(Collectors.joining("&"));
    }

    private String getHeaderContent(HttpHeaders httpHeaders) {
        List<String> headers = new ArrayList<>();
        httpHeaders.forEach((name, values) -> headers.add(name + ": " + String.join(", ", values)));
        return String.join("\n", headers);
    }

    private static HttpHeaders getHeaders(HttpServletRequest request) {
        HttpHeaders headers = new HttpHeaders();
        Collections.list(request.getHeaderNames()).forEach(name -> headers.add(name, request.getHeader(name)));
        return headers;
    }

    private static HttpHeaders getHeaders(HttpServletResponse response) {
        HttpHeaders headers = new HttpHeaders();
        response.getHeaderNames().forEach(name -> headers.add(name, response.getHeader(name)));
        return headers;
    }

    private static org.springframework.util.MultiValueMap<String, String> toMultiValueMap(Map<String, String[]> map) {
        MultiValueMap<String, String> mvMap = new org.springframework.util.LinkedMultiValueMap<>();
        map.forEach((key, values) -> mvMap.put(key, ListUtil.toList(values)));
        return mvMap;
    }


    public HttpDumps skipResponse(boolean skipResponse) {
        this.skipResponse = skipResponse;
        return this;
    }

    public HttpDumps skipBody(boolean skipBody) {
        this.skipBody = skipBody;
        return this;
    }

    public HttpDumps skipHeader(boolean skipHeader) {
        this.skipHeader = skipHeader;
        return this;
    }
}
