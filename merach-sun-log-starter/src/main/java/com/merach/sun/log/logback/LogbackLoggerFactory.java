package com.merach.sun.log.logback;

import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.joran.JoranConfigurator;
import ch.qos.logback.core.joran.spi.JoranException;
import org.slf4j.Logger;

import java.net.URL;
import java.util.Objects;

public class LogbackLoggerFactory {
    private static LoggerContext loggerContext;

    public LogbackLoggerFactory() {
        loggerContext = new LoggerContext();
        loggerContext.setName("logback-web-access");
        loggerContext.reset();
        JoranConfigurator configurator = new JoranConfigurator();
        configurator.setContext(loggerContext);
        try {
            URL url = LogbackLoggerFactory.class.getResource("/logback-web-access.xml");
            Objects.requireNonNull(url);
            configurator.doConfigure(url);
        } catch (JoranException e) {
            System.out.println("Failed to init logback-web-access, " + e.getMessage());
        }
    }

    public Logger getLogger(String name) {
        return loggerContext.getLogger(name);
    }

    public Logger getLogger(Class<?> clazz) {
        return getLogger(clazz.getName());
    }
}