package com.merach.sun.log.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.merach.sun.log.annotation.LogTruncate;

import java.io.IOException;

public class LogTruncateSerializer extends JsonSerializer<String> {
    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider provider)
            throws IOException {
        if (value == null) {
            gen.writeNull();
            return;
        }

        try {
            LogTruncate annotation = gen.getCurrentValue().getClass()
                    .getDeclaredField(gen.getOutputContext().getCurrentName())
                    .getAnnotation(LogTruncate.class);

            if (annotation == null) {
                gen.writeString(value);
                return;
            }

            int maxLength = annotation.value();
            if (maxLength <= 0) {
                gen.writeString("...");
                return;
            }

            String truncated = value.length() < maxLength ?
                    value : value.substring(0, maxLength) + "...";
            gen.writeString(truncated);
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        }
    }
}