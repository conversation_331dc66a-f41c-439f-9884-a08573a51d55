
## 使用

- 引入依赖
```xml
<dependency>
    <groupId>com.merach.sun</groupId>
    <artifactId>merach-sun-log-starter</artifactId>
    <version>${latest.version}</version>
</dependency>
```

- 配置
```yaml
mrk:
  logging:
    web-access:
      aop:
        enabled: true # 启用aop日志
      filter:
        enabled: false # 启用web过滤器日志
```

- logback 配置简化
> 引用 mrk-log-starter 后才可以引用 logback-base.xml
```xml
<configuration debug="true">
    <include resource="com/merach/sun/log/logback/logback-base.xml"/>

    <springProfile name="dev">
        <include resource="com/merach/sun/log/logback/logback-console.xml"/>

        <root level="info">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>

    <springProfile name="!dev">
        <include resource="com/merach/sun/log/logback/logback-file.xml"/>

        <root level="info">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_FILE"/>
            <appender-ref ref="ERROR_FILE"/>
        </root>
    </springProfile>
</configuration>

```


---
## 版本变更
### 0.0.8-RELEASE
1. 增加通用aop打印web访问日志
2. 增加通用aop打印service日志 
3. 增加http请求和响应的日志
4. 增加logback通用模板

## TODO
1. 测试环境调用链实现
