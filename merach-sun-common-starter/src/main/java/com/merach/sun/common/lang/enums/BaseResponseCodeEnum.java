package com.merach.sun.common.lang.enums;

import com.merach.sun.common.lang.exception.BaseException;
import com.merach.sun.common.lang.exception.BusinessException;
import com.merach.sun.common.lang.exception.SystemException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@Deprecated
public enum BaseResponseCodeEnum {

    // ------------------全局错误码-------------------
    /**
     * 成功.
     */
    SUCCESS(200, "OK"),

    /**
     * 默认错误码
     *
     * @see BaseException
     */
    DEFAULT_ERROR(100, "Error"),

    /**
     * 默认参数错误
     */
    DEFAULT_PARAM_ERROR(400, "Param Error"),

    /**
     * 默认认证错误
     */
    DEFAULT_UNAUTHORIZED_ERROR(401, "UNAUTHORIZED"),

    /**
     * 默认权限错误
     */
    DEFAULT_FORBIDDEN_ERROR(403, "FORBIDDEN"),

    /**
     * 默认系统错误码
     *
     * @see SystemException
     */
    DEFAULT_SYSTEM_ERROR(500, "System Error"),

    /**
     * 默认业务错误码
     *
     * @see BusinessException
     */
    DEFAULT_BUSINESS_ERROR(10000, "Business Error");

    private final int code;

    private final String msg;

}
