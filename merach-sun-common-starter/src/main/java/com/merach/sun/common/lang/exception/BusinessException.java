package com.merach.sun.common.lang.exception;

import com.merach.sun.common.lang.enums.BaseResponseCodeEnum;
import lombok.Getter;

/**
 * BusinessException
 * code > 10000
 *
 * <AUTHOR>
 * @version 1.0 2020/1/8
 * @since 1.0
 */
@Getter
@Deprecated
public class BusinessException extends BaseException {

    /**
     * 默认业务错误码
     */
    static final int DEFAULT_BIZ_CODE = BaseResponseCodeEnum.DEFAULT_BUSINESS_ERROR.getCode();

    public BusinessException(String message) {
        this(DEFAULT_BIZ_CODE, message);
    }

    public BusinessException(int code, String message) {
        super(code, message);
    }

    @Override
    public synchronized Throwable fillInStackTrace() {
        return this;
    }

}
