package com.merach.sun.common.lang.exception;

import com.merach.sun.common.lang.enums.BaseResponseCodeEnum;
import lombok.Getter;

/**
 * BusinessException
 * code > 100 & code < 10000
 *
 * <AUTHOR>
 * @version 1.0 2020/1/8
 * @since 1.0
 */
@Getter
@Deprecated
public class SystemException extends BaseException {

    /**
     * 默认系统错误码
     */
    static final int DEFAULT_SYSTEM_CODE = BaseResponseCodeEnum.DEFAULT_SYSTEM_ERROR.getCode();

    public SystemException() {
        this(DEFAULT_SYSTEM_CODE, "System error");
    }

    public SystemException(String message) {
        this(DEFAULT_SYSTEM_CODE, message);
    }

    public SystemException(int code, String message) {
        super(code, message);
    }

}
