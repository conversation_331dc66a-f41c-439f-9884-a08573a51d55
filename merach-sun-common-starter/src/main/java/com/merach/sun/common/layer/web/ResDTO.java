package com.merach.sun.common.layer.web;

import com.merach.sun.common.lang.enums.BaseResponseCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.MediaType;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Deprecated
public class ResDTO<T> {

    private Integer status;

    private String message;

    private T data;

    public ResDTO(int status, String message) {
        this.status = status;
        this.message = message;
    }

    public static <T> ResDTO<T> ok() {
        return new ResDTO<>(BaseResponseCodeEnum.SUCCESS.getCode(), BaseResponseCodeEnum.SUCCESS.getMsg());
    }

    public static <T> ResDTO<T> ok(T data) {
        return new ResDTO<>(BaseResponseCodeEnum.SUCCESS.getCode(), BaseResponseCodeEnum.SUCCESS.getMsg(), data);
    }

    public static <T> ResDTO<T> fail() {
        return new ResDTO<>(BaseResponseCodeEnum.DEFAULT_ERROR.getCode(), BaseResponseCodeEnum.DEFAULT_ERROR.getMsg());
    }

    public static <T> ResDTO<T> fail(String message) {
        return new ResDTO<>(BaseResponseCodeEnum.DEFAULT_ERROR.getCode(), message);
    }

    public static <T> ResDTO<T> fail(int status, String message) {
        return new ResDTO<>(status, message);
    }

    public static <T> ResDTO<T> paramFail(String message) {
        return new ResDTO<>(BaseResponseCodeEnum.DEFAULT_PARAM_ERROR.getCode(), message);
    }

    public static <T> ResDTO<T> fail(BaseResponseCodeEnum baseResponseCodeEnum) {
        return new ResDTO<>(baseResponseCodeEnum.getCode(), baseResponseCodeEnum.getMsg());
    }

    public static <T> boolean isFail(ResDTO<T> resDTO) {
        if (resDTO == null) {
            return true;
        }

        return resDTO.getStatus() != BaseResponseCodeEnum.SUCCESS.getCode();
    }

    public static void writer(BaseResponseCodeEnum baseResponseCodeEnum, HttpServletResponse response) {
        writer(baseResponseCodeEnum.getCode(), baseResponseCodeEnum.getMsg(), response);
    }

    public static void writer(int status, String message, HttpServletResponse response) {
        String data = "{\"status\": \"" + status + "\", \"message\": \"" + message + "\"}";
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");
        try (PrintWriter printWriter = response.getWriter()) {
            printWriter.write(data);
            printWriter.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
