package com.merach.sun.common.utils;

import com.ql.util.express.ExpressRunner;
import com.ql.util.express.IExpressContext;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class QLExpressUtils {
    private static final ExpressRunner runner = new ExpressRunner();

    /**
     * QLExpress 执行器
     */
    public static Object execute(String expressString, IExpressContext<String, Object> context) {
        Object result = null;
        List<String> errorList = new ArrayList<>();
        try {
            result = execute(expressString, context, errorList, true, false);
        } catch (Exception e) {
            log.error("QLExpressUtilError, expressString:{}, context:{}, errorList:{}", expressString, context, errorList);
        }
        return result;
    }

    public static Object execute(String expressString, IExpressContext<String, Object> context, List<String> errorList, Boolean isCache, Boolean isTrace) {
        Object result = null;
        try {
            result = runner.execute(expressString, context, errorList, isCache, isTrace);
        } catch (Exception e) {
            log.error("QLExpressUtilError, expressString:{}, context:{}, errorList:{}", expressString, context, errorList);
        }
        return result;
    }
}
