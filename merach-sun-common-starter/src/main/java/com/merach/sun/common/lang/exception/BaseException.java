package com.merach.sun.common.lang.exception;

import com.merach.sun.common.lang.enums.BaseResponseCodeEnum;
import lombok.Getter;

/**
 * BaseException
 *
 * <AUTHOR>
 * @version 1.0 2020/1/8
 * @since 1.0
 */
@Getter
@Deprecated
public abstract class BaseException extends RuntimeException {

    /**
     * 默认错误码
     */
    static final int DEFAULT_CODE = BaseResponseCodeEnum.DEFAULT_ERROR.getCode();

    protected final int code;

    protected BaseException(String message) {
        this(DEFAULT_CODE, message);
    }

    protected BaseException(int code, String message) {
        super(message);
        this.code = code;
    }

}
