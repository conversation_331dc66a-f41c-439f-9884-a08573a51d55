package com.merach.sun.common.layer.web;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/24 10:22
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@Deprecated
public class PageDTO<T> {

    private List<T> records;

    private Long total;

    public PageDTO() {
        this.total = 0L;
    }

    public static <T> PageDTO<T> of(List<T> records, long total) {
        return new PageDTO<>(records, total);
    }

}
