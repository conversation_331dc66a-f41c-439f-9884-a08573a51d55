package com.merach.sun.common.lang.exception;

import com.merach.sun.common.lang.enums.BaseResponseCodeEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/4/6 10:36
 */
@Getter
@Deprecated
public class ParamException extends BaseException {

    public ParamException(String message) {
        this(BaseResponseCodeEnum.DEFAULT_PARAM_ERROR.getCode(), message);
    }

    public ParamException(int code, String message) {
        super(code, message);
    }

    @Override
    public synchronized Throwable fillInStackTrace() {
        return this;
    }
}
