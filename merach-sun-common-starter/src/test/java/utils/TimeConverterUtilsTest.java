package utils;

import com.merach.sun.common.utils.time.TimeConverterUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;
import java.util.Date;

class TimeConverterUtilsTest {
    
    @Test
    void testToAndFromDate() {
        LocalDateTime now = LocalDateTime.now();
        Date date = TimeConverterUtils.toDate(now);
        LocalDateTime converted = TimeConverterUtils.fromDate(date);
        
        Assertions.assertEquals(now.truncatedTo(java.time.temporal.ChronoUnit.MILLIS),
                    converted.truncatedTo(java.time.temporal.ChronoUnit.MILLIS));
    }
    
    @Test
    void testToAndFromTimestamp() {
        LocalDateTime now = LocalDateTime.now();
        long timestamp = TimeConverterUtils.toTimestamp(now);
        LocalDateTime converted = TimeConverterUtils.fromTimestamp(timestamp);
        
        Assertions.assertEquals(now.truncatedTo(java.time.temporal.ChronoUnit.MILLIS),
                    converted.truncatedTo(java.time.temporal.ChronoUnit.MILLIS));
    }
    
    @Test
    void testSpecificDateTime() {
        LocalDateTime expected = LocalDateTime.of(2023, 1, 1, 12, 30, 45);
        Date date = TimeConverterUtils.toDate(expected);
        LocalDateTime actual = TimeConverterUtils.fromDate(date);
        
        Assertions.assertEquals(expected, actual);
    }
}