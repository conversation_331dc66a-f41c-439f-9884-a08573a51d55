package utils;

import com.merach.sun.common.utils.time.TimeRangeUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

class TimeRangeUtilsTest {

    @Test
    void testStartAndEndOfDay() {
        LocalDate date = LocalDate.of(2023, 1, 1);
        
        LocalDateTime startOfDay = TimeRangeUtils.startOfDay(date);
        Assertions.assertEquals(LocalTime.MIDNIGHT, startOfDay.toLocalTime());
        Assertions.assertEquals(date, startOfDay.toLocalDate());
        
        LocalDateTime endOfDay = TimeRangeUtils.endOfDay(date);
        Assertions.assertEquals(23, endOfDay.getHour());
        Assertions.assertEquals(59, endOfDay.getMinute());
        Assertions.assertEquals(59, endOfDay.getSecond());
        Assertions.assertEquals(0, endOfDay.getNano());
    }

    @Test
    void testStartAndEndOfWeek() {
        LocalDate wednesday = LocalDate.of(2023, 1, 4); // A Wednesday
        
        LocalDateTime startOfWeek = TimeRangeUtils.startOfWeek(wednesday);
        Assertions.assertEquals(LocalDate.of(2023, 1, 2), startOfWeek.toLocalDate()); // Should be Monday
        Assertions.assertEquals(LocalTime.MIDNIGHT, startOfWeek.toLocalTime());
        
        LocalDateTime endOfWeek = TimeRangeUtils.endOfWeek(wednesday);
        Assertions.assertEquals(LocalDate.of(2023, 1, 8), endOfWeek.toLocalDate()); // Should be Sunday
        Assertions.assertEquals(23, endOfWeek.getHour());
        Assertions.assertEquals(59, endOfWeek.getMinute());
        Assertions.assertEquals(59, endOfWeek.getSecond());
    }

    @Test
    void testStartAndEndOfMonth() {
        LocalDate date = LocalDate.of(2023, 1, 15); // Middle of January
        
        LocalDateTime startOfMonth = TimeRangeUtils.startOfMonth(date);
        Assertions.assertEquals(LocalDate.of(2023, 1, 1), startOfMonth.toLocalDate());
        Assertions.assertEquals(LocalTime.MIDNIGHT, startOfMonth.toLocalTime());
        
        LocalDateTime endOfMonth = TimeRangeUtils.endOfMonth(date);
        Assertions.assertEquals(LocalDate.of(2023, 1, 31), endOfMonth.toLocalDate());
        Assertions.assertEquals(23, endOfMonth.getHour());
        Assertions.assertEquals(59, endOfMonth.getMinute());
        Assertions.assertEquals(59, endOfMonth.getSecond());
    }

    @Test
    void testIsSameDay() {
        LocalDateTime time1 = LocalDateTime.of(2023, 1, 1, 12, 0);
        LocalDateTime time2 = LocalDateTime.of(2023, 1, 1, 23, 59);
        LocalDateTime time3 = LocalDateTime.of(2023, 1, 2, 0, 0);
        
        Assertions.assertTrue(TimeRangeUtils.isSameDay(time1, time2));
        Assertions.assertFalse(TimeRangeUtils.isSameDay(time1, time3));
    }

    @Test
    void testIsBetween() {
        LocalDateTime start = LocalDateTime.of(2023, 1, 1, 12, 0);
        LocalDateTime end = LocalDateTime.of(2023, 1, 2, 12, 0);
        
        LocalDateTime between = LocalDateTime.of(2023, 1, 1, 18, 0);
        LocalDateTime before = LocalDateTime.of(2023, 1, 1, 11, 59);
        LocalDateTime after = LocalDateTime.of(2023, 1, 2, 12, 1);
        
        Assertions.assertTrue(TimeRangeUtils.isBetween(between, start, end));
        Assertions.assertFalse(TimeRangeUtils.isBetween(before, start, end));
        Assertions.assertFalse(TimeRangeUtils.isBetween(after, start, end));
    }

    @Test
    void testBetweenCalculations() {
        LocalDateTime start = LocalDateTime.of(2023, 1, 1, 12, 0);
        LocalDateTime end = LocalDateTime.of(2023, 1, 3, 14, 30);
        
        Assertions.assertEquals(2, TimeRangeUtils.betweenDays(start, end));
        Assertions.assertEquals(50, TimeRangeUtils.betweenHours(start, end));
        Assertions.assertEquals(3030, TimeRangeUtils.betweenMinutes(start, end));
        Assertions.assertEquals(181800, TimeRangeUtils.betweenSeconds(start, end));
        
        // Test reverse order (should be negative)
        Assertions.assertEquals(-2, TimeRangeUtils.betweenDays(end, start));
    }
}