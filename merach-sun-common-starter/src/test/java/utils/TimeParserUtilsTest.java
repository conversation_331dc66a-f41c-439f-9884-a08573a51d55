package utils;

import com.merach.sun.common.utils.time.TimeParserUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeParseException;

class TimeParserUtilsTest {

    @Test
    void testParseWithFormat() {
        String dateStr = "2023-01-01 12:30:45";
        LocalDateTime expected = LocalDateTime.of(2023, 1, 1, 12, 30, 45);
        LocalDateTime actual = TimeParserUtils.parse(dateStr, "yyyy-MM-dd HH:mm:ss");
        Assertions.assertEquals(expected, actual);
    }

    @Test
    void testParseWithCustomFormat() {
        String dateStr = "2023年01月01日 12时30分";
        LocalDateTime expected = LocalDateTime.of(2023, 1, 1, 12, 30);
        LocalDateTime actual = TimeParserUtils.parse(dateStr, "yyyy年MM月dd日 HH时mm分");
        Assertions.assertEquals(expected, actual);
    }

    @Test
    void testParseInvalidFormat() {
        String dateStr = "2023-01-01 12:30:45";
        Assertions.assertThrows(DateTimeParseException.class, () -> {
            TimeParserUtils.parse(dateStr, "yyyy/MM/dd HH:mm:ss");
        });
    }

    @Test
    void testParseInvalidDate() {
        String dateStr = "2023-13-01 12:30:45";  // Invalid month
        Assertions.assertThrows(DateTimeParseException.class, () -> {
            TimeParserUtils.parse(dateStr, "yyyy-MM-dd HH:mm:ss");
        });
    }
}