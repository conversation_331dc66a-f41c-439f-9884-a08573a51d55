package utils;

import com.merach.sun.common.utils.time.TimeFormatterUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.time.Duration;
import java.time.LocalDateTime;

class TimeFormatterUtilsTest {

    @Test
    void testFormat() {
        LocalDateTime time = LocalDateTime.of(2023, 1, 1, 12, 30, 45);
        Assertions.assertEquals("2023-01-01 12:30:45", TimeFormatterUtils.format(time));
    }

    @Test
    void testFormatMillis() {
        LocalDateTime time = LocalDateTime.of(2023, 1, 1, 12, 30, 45, 123_000_000);
        Assertions.assertEquals("2023-01-01 12:30:45.123", TimeFormatterUtils.formatMillis(time));
    }

    @Test
    void testFormatWithPattern() {
        LocalDateTime time = LocalDateTime.of(2023, 1, 1, 12, 30, 45);
        Assertions.assertEquals("2023年01月01日 12时30分",
                    TimeFormatterUtils.format(time, "yyyy年MM月dd日 HH时mm分"));
    }

    @Test
    void testDescribeDuration() {
        Duration duration = Duration.ofDays(2)
                                  .plusHours(3)
                                  .plusMinutes(45)
                                  .plusSeconds(30);
        Assertions.assertEquals("2d3h45m30s", TimeFormatterUtils.describeDuration(duration));
        
        // Test single unit
        Assertions.assertEquals("30s", TimeFormatterUtils.describeDuration(Duration.ofSeconds(30)));
        Assertions.assertEquals("45m", TimeFormatterUtils.describeDuration(Duration.ofMinutes(45)));
        
        // Test zero duration
        Assertions.assertEquals("0s", TimeFormatterUtils.describeDuration(Duration.ZERO));
    }

    @Test
    void testDescribeTimeDiff() {
        LocalDateTime start = LocalDateTime.of(2023, 1, 1, 12, 0);
        LocalDateTime end = LocalDateTime.of(2023, 1, 2, 15, 30);
        
        Assertions.assertEquals("1d3h30m", TimeFormatterUtils.describeTimeDiff(start, end));
        // Test reverse order (should give same result)
        Assertions.assertEquals("1d3h30m", TimeFormatterUtils.describeTimeDiff(end, start));
    }
}