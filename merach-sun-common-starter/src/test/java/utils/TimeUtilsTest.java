package utils;

import com.merach.sun.common.utils.time.TimeUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

class TimeUtilsTest {

    @Test
    void testAddDays() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime result = TimeUtils.addDays(now, 1);
        Assertions.assertEquals(1, ChronoUnit.DAYS.between(now, result));

        result = TimeUtils.addDays(now, -1);
        Assertions.assertEquals(-1, ChronoUnit.DAYS.between(now, result));
    }

    @Test
    void testAddHours() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime result = TimeUtils.addHours(now, 2);
        Assertions.assertEquals(2, ChronoUnit.HOURS.between(now, result));

        result = TimeUtils.addHours(now, -2);
        Assertions.assertEquals(-2, ChronoUnit.HOURS.between(now, result));
    }

    @Test
    void testAddMinutes() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime result = TimeUtils.addMinutes(now, 30);
        Assertions.assertEquals(30, ChronoUnit.MINUTES.between(now, result));

        result = TimeUtils.addMinutes(now, -30);
        Assertions.assertEquals(-30, ChronoUnit.MINUTES.between(now, result));
    }

    @Test
    void testTruncateToSeconds() {
        LocalDateTime time = LocalDateTime.parse("2023-01-01T12:30:45.123");
        LocalDateTime result = TimeUtils.truncateToSeconds(time);
        Assertions.assertEquals(0, result.getNano());
        Assertions.assertEquals(45, result.getSecond());
    }
}