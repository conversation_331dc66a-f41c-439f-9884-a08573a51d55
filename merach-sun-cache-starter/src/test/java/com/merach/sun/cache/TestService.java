package com.merach.sun.cache;

import com.merach.sun.cache.config.User;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

@Component
public class TestService {

    @Cacheable(value = "test", key = "#id")
    public User getById(long id) {
        System.out.println("getById " + id);
        return new User(123);
    }

    @Cacheable(value = "test", key = "#user.id")
    public User getByUserId(User user) {
        System.out.println("getByUserId " + user.getId());
        return new User(123);
    }


    @CacheEvict(value = "test", key = "#user.id")
    public void updateById(User user) {
        System.out.println("updateById " + user.getId());
    }
}
