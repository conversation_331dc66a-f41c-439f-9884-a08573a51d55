package com.merach.sun.cache.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
public class User {
    private int id = 1;
    private String name = "test_name";
    private LocalDate localDate = LocalDate.now();
    private LocalDateTime localDateTime = LocalDateTime.now();
    private Date date = new Date();
    private Map<String, String> map = Map.of("a", "b", "c", "d");
    private List<String> list = List.of("a", "b", "c", "d");
    private Map<String, List<String>> mapList = Map.of("a", List.of("a1", "a2"), "b", List.of("b1", "b2"));
    private Map<String, List<A>> userMap = Map.of("a", List.of(new A(), new A()));

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class A {
        private int id = 1;
    }

    public User(int id) {
        this.id = id;
    }
}