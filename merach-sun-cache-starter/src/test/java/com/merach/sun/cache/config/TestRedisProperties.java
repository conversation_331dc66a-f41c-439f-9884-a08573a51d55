package com.merach.sun.cache.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Getter
@Configuration
public class TestRedisProperties {
    private final int redisPort;
    private final String redisHost;

    public TestRedisProperties(
      @Value("${spring.data.redis.port}") int redisPort,
      @Value("${spring.data.redis.host}") String redisHost) {
        this.redisPort = redisPort;
        this.redisHost = redisHost;
    }
}