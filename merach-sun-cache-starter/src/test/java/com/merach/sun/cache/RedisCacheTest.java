package com.merach.sun.cache;

import com.merach.sun.cache.config.TestRedisConfiguration;
import com.merach.sun.cache.config.User;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(OutputCaptureExtension.class)
@SpringBootTest(classes = {TestApplication.class, TestRedisConfiguration.class})
public class RedisCacheTest {

    @Autowired
    private TestService testService;

    @Test
    public void testAop(CapturedOutput capturedOutput) {
        testService.getById(1L);
        assertThat(capturedOutput.toString()).contains("getById 1");

        testService.getById(1L);
        assertThat(capturedOutput.toString()).contains("cacheEvent=hit");
        assertThat(lastLine(capturedOutput)).doesNotContain("getById 1");

        testService.getByUserId(new User(2));
        assertThat(capturedOutput.toString()).contains("getByUserId 2");

        testService.getByUserId(new User(2));
        assertThat(capturedOutput.toString()).contains("getByUserId 2");

        testService.updateById(new User(2));
        assertThat(capturedOutput.toString()).contains("updateById 2").contains("cacheEvent=evict");
        testService.getById(2L);
        assertThat(capturedOutput.toString()).contains("getByUserId 2");
    }

    private String lastLine(CapturedOutput capturedOutput) {
        String[] lines = capturedOutput.toString().split("\\R"); // \R = any line break
        return lines[lines.length - 1];
    }
}