package com.merach.sun.cache.logger;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.cache.RedisCache;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheWriter;

@Slf4j
public class LoggingRedisCache extends RedisCache {

    public LoggingRedisCache(String name, RedisCacheWriter cacheWriter, RedisCacheConfiguration configuration) {
        super(name, cacheWriter, configuration);
    }

    @Override
    public ValueWrapper get(Object key) {
        ValueWrapper wrapper = super.get(key);
        if (wrapper != null) {
            log.info("cacheEvent=hit name={} key={}", getName(), key);
        } else {
            log.info("cacheEvent=miss name={} key={}", getName(), key);
        }
        return wrapper;
    }

    @Override
    public void put(Object key, Object value) {
        log.info("cacheEvent=put name={} key={}", getName(), key);
        super.put(key, value);
    }

    @Override
    public void evict(Object key) {
        log.info("cacheEvent=evict name={} key={}", getName(), key);
        super.evict(key);
    }

    @Override
    public void clear() {
        log.info("cacheEvent=clear name={}", getName());
        super.clear();
    }
}