package com.merach.sun.cache.logger;

import org.springframework.data.redis.cache.RedisCache;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;

public class LoggingRedisCacheManager extends RedisCacheManager {
    private final RedisCacheWriter cacheWriter;
    private final RedisCacheConfiguration defaultConfiguration;

    public LoggingRedisCacheManager(RedisCacheWriter cacheWriter, RedisCacheConfiguration defaultConfiguration) {
        super(cacheWriter, defaultConfiguration);
        this.cacheWriter = cacheWriter;
        this.defaultConfiguration = defaultConfiguration;
    }

    @Override
    protected RedisCache createRedisCache(String name, RedisCacheConfiguration configuration) {
        return new LoggingRedisCache(name, cacheWriter, configuration != null ? configuration : defaultConfiguration);
    }
}