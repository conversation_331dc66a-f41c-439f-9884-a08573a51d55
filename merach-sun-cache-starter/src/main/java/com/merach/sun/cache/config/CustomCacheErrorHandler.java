package com.merach.sun.cache.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.data.redis.RedisConnectionFailureException;

@Slf4j
public class CustomCacheErrorHandler  implements CacheErrorHandler {
    @Override
    public void handleCacheGetError(RuntimeException exception, Cache cache, Object key) {
        handleError(exception, key, "get");
    }

    @Override
    public void handleCachePutError(RuntimeException exception, Cache cache, Object key, Object value) {
        handleError(exception, key, "put");
    }

    @Override
    public void handleCacheEvictError(RuntimeException exception, Cache cache, Object key) {
        handleError(exception, key, "evict");
    }

    @Override
    public void handleCacheClearError(RuntimeException e, Cache cache) {
        log.error("cacheEvent=clear status=failed key={} reason=exception:{} message={}",
                cache.getName(), e.getClass().getSimpleName(), e.getMessage(), e);
    }

    private void handleError(RuntimeException e, Object key, String operation) {
        if (e instanceof RedisConnectionFailureException) {
            log.error("cacheEvent={} status=failed key={} reason=exception:{} message={}",
                    operation,key, e.getClass().getSimpleName(), e.getMessage());
        } else {
            log.error("cacheEvent={} status=failed key={} reason=exception:{} message={}",
                    operation, key, e.getClass().getSimpleName(), e.getMessage(), e);
        }
    }
}
