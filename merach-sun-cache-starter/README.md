
## 使用

```java
// 使用 spring @Cacheable 注解
@Cacheable(value = "test", key = "#id")
public User getById(long id) {
    System.out.println("getById " + id);
    return new User(123);
}

@Cacheable(value = "test", key = "#user.id")
public User getByUserId(User user) {
    System.out.println("getByUserId " + user.getId());
    return new User(123);
}

@CacheEvict(value = "test", key = "#user.id")
public void updateById(User user) {
    System.out.println("updateById " + user.getId());
}

@CachePut
public User updateById(User user) {}
```



## 版本变更
### 0.0.7-RELEASE 版本
1. 基于fastjson spring-cache 封装缓存