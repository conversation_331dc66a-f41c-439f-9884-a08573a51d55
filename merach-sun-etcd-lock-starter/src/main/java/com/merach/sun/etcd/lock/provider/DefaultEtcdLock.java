package com.merach.sun.etcd.lock.provider;

import com.merach.sun.etcd.lock.EtcdLock;
import com.merach.sun.etcd.lock.config.EtcdLockProperties;
import io.etcd.jetcd.Client;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

@Slf4j
public class DefaultEtcdLock implements EtcdLock {
    private final EtcdLockClient lock;
    private final EtcdLockProperties config;

    public DefaultEtcdLock(Client etcdClient, String key, EtcdLockProperties properties) {
        this.lock = new EtcdLockClient(etcdClient, properties.getLockPrefix() + key, properties.getLeaseTimeSec());
        this.config = properties;
    }

    @Override
    public boolean tryLock() {
        return tryLock(0, TimeUnit.SECONDS);
    }

    @Override
    public boolean tryLock(long waitTime, TimeUnit unit) {
        return lock.tryLock(waitTime, unit);
    }

    @Override
    public void unlock() {
        lock.unlock();
    }

    @Override
    public boolean isLocked() {
        return lock.isLocked();
    }

    @Override
    public boolean isHeldByCurrentThread() {
        return lock.isHeldByCurrentThread();
    }

    @Override
    public int getHoldCount() {
        return lock.getHoldCount();
    }
}