package com.merach.sun.etcd.lock.config;

import com.merach.sun.etcd.lock.EtcdLockManager;
import com.merach.sun.etcd.lock.aspect.EtcdLockAspect;
import com.merach.sun.etcd.lock.provider.EtcdLockManagerImpl;
import io.etcd.jetcd.Client;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@EnableConfigurationProperties({EtcdClientProperties.class, EtcdLockProperties.class})
public class EtcdLockAutoConfiguration {

    @Bean
    public EtcdLockAspect etcdLockAspect(EtcdLockManager etcdLockManager) {
        return new EtcdLockAspect(etcdLockManager);
    }

    @Bean
    public EtcdLockManager distributedLockManager(Client etcdClient, EtcdLockProperties properties) {
        log.info("initEvent=etcdLock");
        return new EtcdLockManagerImpl(etcdClient, properties);
    }

    @Bean
    @ConditionalOnProperty("etcd")
    public Client etcdClient(EtcdClientProperties properties) {
        log.info("initEvent=etcdClient");
        return Client.builder()
                .endpoints(properties.getEndpoints().split(","))
                .build();
    }
} 