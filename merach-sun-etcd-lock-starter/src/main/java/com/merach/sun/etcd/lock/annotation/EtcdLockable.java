package com.merach.sun.etcd.lock.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

/**
 * etcd 实现的分布式锁
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface EtcdLockable {

    /**
     * 缓存key，只支持静态string
     */
    String value() default "";

    /**
     * 缓存key，支持spel string
     */
    String key() default "";

    long waitTime() default 0;

    TimeUnit timeUnit() default TimeUnit.SECONDS;
}
