package com.merach.sun.etcd.lock.provider;

import io.etcd.jetcd.ByteSequence;
import io.etcd.jetcd.Client;
import io.etcd.jetcd.Lease;
import io.etcd.jetcd.Lock;
import io.etcd.jetcd.Watch;
import io.etcd.jetcd.lease.LeaseKeepAliveResponse;
import io.etcd.jetcd.lock.LockResponse;
import io.etcd.jetcd.options.WatchOption;
import io.etcd.jetcd.watch.WatchEvent;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Slf4j
public class EtcdLockClient {
    private final Lock lockClient;
    private final Lease leaseClient;
    private final Watch watchClient;
    private final String lockName;
    private final long leaseTimeMs;

    private final static ThreadLocal<Map<String, LockContext>> THREAD_HELD_LOCKS = ThreadLocal.withInitial(HashMap::new);

    public EtcdLockClient(Client etcdClient, String lockName, long leaseTimeMs) {
        this.lockName = lockName;
        this.leaseTimeMs = leaseTimeMs;
        this.lockClient = etcdClient.getLockClient();
        this.leaseClient = etcdClient.getLeaseClient();
        this.watchClient = etcdClient.getWatchClient();
    }

    public boolean tryLock(long waitTime, TimeUnit timeUnit) {
        if (isHeldByCurrentThread()) {
            incrementHoldCount();
            return true;
        }

        long leaseId = grantLeaseId(leaseTimeMs, TimeUnit.MICROSECONDS);
        // Step 1: Try to acquire lock immediately
        boolean locked = tryAcquireLockOnce(leaseId);
        if (locked) {
            return true;
        }

        if (waitTime == 0) {
            log.warn("Failed to obtain lock immediately. key={}", lockName);
            return false;
        }

        // Step 2: Wait for DELETE event on lock key
        boolean released = waitLockRelease(waitTime, timeUnit);
        if (!released) {
            log.warn("Failed to wait lock release. key={}", lockName);
            return false;
        }
        return tryAcquireLockOnce(leaseId);
    }

    private long grantLeaseId(long leaseTime, TimeUnit unit) {
        try {
            return leaseClient.grant(unit.toSeconds(leaseTime)).get().getID();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IllegalStateException("Interrupted while grantLeaseId. key=" + lockName, e);
        } catch (ExecutionException e) {
            throw new IllegalStateException("Failed to grantLeaseId. key=" + lockName, e);
        }
    }

    private boolean tryAcquireLockOnce(long leaseId) {
        try {
            ByteSequence keySequence = ByteSequence.from(lockName, StandardCharsets.UTF_8);
            LockResponse resp = lockClient.lock(keySequence, leaseId).get(300, TimeUnit.MILLISECONDS);
            leaseClient.keepAlive(leaseId, new KeepAliveObserver());
            addLock(leaseId, resp.getKey());
            return true;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IllegalStateException("Interrupted while lock once. key=" + lockName, e);
        } catch (ExecutionException e) {
            throw new IllegalStateException("Failed to lock once. key=" + lockName, e);
        } catch (TimeoutException e) {
            return false;
        }
    }

    private boolean waitLockRelease(long waitTime, TimeUnit timeUnit) {
        ByteSequence keySequence = ByteSequence.from(lockName, StandardCharsets.UTF_8);
        CountDownLatch latch = new CountDownLatch(1);
        try (Watch.Watcher ignored = watchClient.watch(keySequence, WatchOption.builder().withNoPut(true).build(),
                response -> {
                    for (WatchEvent event : response.getEvents()) {
                        if (event.getEventType() == WatchEvent.EventType.DELETE) {
                            latch.countDown();
                        }
                    }
                })) {
            return latch.await(waitTime, timeUnit);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Interrupted while wait lock. key={}", lockName, e);
            return false;
        }
    }

    public void unlock() {
        LockContext ctx = getLock();
        if (ctx == null) {
            return;
        }

        if (decrementHoldCount() == 0) {
            try {
                lockClient.unlock(ctx.etcdKey).get();
                leaseClient.revoke(ctx.leaseId).get();
                removeLock();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("Interrupted while unlocking. key={}", lockName, e);
            } catch (ExecutionException e) {
                log.error("Failed to unlock. key={}", lockName, e);
            }
        }
    }

    private class KeepAliveObserver implements StreamObserver<LeaseKeepAliveResponse> {
        @Override
        public void onNext(LeaseKeepAliveResponse value) {
            log.info("Lease keeps alive for {}s:", value.getTTL());
        }

        @Override
        public void onError(Throwable t) {
            removeLock();
            log.error("Lease renewal Exception.", t.fillInStackTrace());
        }

        @Override
        public void onCompleted() {
            removeLock();
            log.info("Lease renewal completed.");
        }
    }

    public boolean isHeldByCurrentThread() {
        return THREAD_HELD_LOCKS.get().containsKey(lockName);
    }

    public int getHoldCount() {
        return getLock().holdCount;
    }

    private void addLock(long leaseId, ByteSequence etcdKey) {
        LockContext ctx = new LockContext(leaseId, etcdKey);
        THREAD_HELD_LOCKS.get().put(lockName, ctx);
    }

    private LockContext getLock() {
        return THREAD_HELD_LOCKS.get().get(lockName);
    }

    private void removeLock() {
        THREAD_HELD_LOCKS.get().remove(lockName);
    }

    public boolean isLocked() {
        return isHeldByCurrentThread();
    }

    private void incrementHoldCount() {
        LockContext ctx = getLock();
        if (ctx != null) {
            ctx.holdCount++;
        }
    }

    private int decrementHoldCount() {
        LockContext ctx = getLock();
        if (ctx != null) {
            return --ctx.holdCount;
        }
        return 0;
    }

    private static class LockContext {
        final long leaseId;
        final ByteSequence etcdKey;
        int holdCount = 1;

        LockContext(long leaseId, ByteSequence etcdKey) {
            this.leaseId = leaseId;
            this.etcdKey = etcdKey;
        }
    }
}
