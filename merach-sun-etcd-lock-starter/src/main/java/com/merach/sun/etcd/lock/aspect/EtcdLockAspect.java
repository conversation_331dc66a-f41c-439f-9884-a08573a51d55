package com.merach.sun.etcd.lock.aspect;

import com.merach.sun.common.exception.DistributedLockException;
import com.merach.sun.etcd.lock.EtcdLock;
import com.merach.sun.etcd.lock.EtcdLockManager;
import com.merach.sun.etcd.lock.annotation.EtcdLockable;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;

@Order(10)
@Aspect
@RequiredArgsConstructor
public class EtcdLockAspect {
    private final EtcdLockManager lockManager;

    @Pointcut("@within(org.springframework.stereotype.Component) || @within(org.springframework.stereotype.Service)")
    private void servicePointcut() {
    }

    @Around("@annotation(annotation) && servicePointcut()")
    public Object around(ProceedingJoinPoint joinPoint, EtcdLockable annotation) throws Throwable {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        String lockName = parseKey(annotation, method, joinPoint.getArgs());

        EtcdLock lock = lockManager.getLock(lockName);
        // 加锁
        boolean locked = lock.tryLock(annotation.waitTime(), annotation.timeUnit());
        if (!locked) {
            throw new DistributedLockException("Failed to obtain lock. key= " + lockName);
        }

        try {
            return joinPoint.proceed();
        } finally {
            lock.unlock();
        }
    }

    private static String parseKey(EtcdLockable annotation, Method method, Object[] args) {
        StringBuilder buf = new StringBuilder();
        if (StringUtils.hasText(annotation.value())) {
            buf.append(annotation.value());
        }

        if (StringUtils.hasText(annotation.key())) {
            if (buf.length() > 0) {
                buf.append("_");
            }
            buf.append(SpelKeyResolver.parse(annotation.key(), method, args));
        }

        if (buf.length() == 0) {
            throw new IllegalArgumentException("RedisLockable must set key or value");
        }
        return buf.toString();
    }
}
