package com.merach.sun.etcd.lock.provider;

import com.merach.sun.etcd.lock.EtcdLock;
import com.merach.sun.etcd.lock.EtcdLockManager;
import com.merach.sun.etcd.lock.config.EtcdLockProperties;
import io.etcd.jetcd.Client;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class EtcdLockManagerImpl implements EtcdLockManager {
    private final Client etcdClient;
    private final EtcdLockProperties properties;

    @Override
    public EtcdLock getLock(String key){
        return new DefaultEtcdLock(etcdClient, key, properties);
    }
}
