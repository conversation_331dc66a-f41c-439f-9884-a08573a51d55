package com.merach.sun.loadbalancer;


import com.alibaba.cloud.nacos.ConditionalOnNacosDiscoveryEnabled;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.loadbalancer.annotation.LoadBalancerClients;
import org.springframework.context.annotation.Configuration;

/**
 * {@link org.springframework.boot.autoconfigure.EnableAutoConfiguration
 * Auto-configuration} that sets up LoadBalancer for Nacos.
 */
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties
@ConditionalOnNacosDiscoveryEnabled
@ConditionalOnExpression(
        "${spring.cloud.loadbalancer.route-tag.enabled:false} && " +
                "${spring.cloud.loadbalancer.nacos.enabled:false}==false"
)
@LoadBalancerClients(defaultConfiguration = RouteTagLoadBalancerConfig.class)
@AutoConfiguration
public class RouteTagLoadBalancerAutoConfiguration {
}
