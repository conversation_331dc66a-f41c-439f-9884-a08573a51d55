package com.merach.sun.loadbalancer;

import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnExpression(
        "${spring.cloud.loadbalancer.route-tag.enabled:false} && " +
                "${spring.cloud.loadbalancer.nacos.enabled:false}==false"
)
@Slf4j
public class RouteTagInitializerAutoConfiguration implements BeanPostProcessor {

    private boolean initFlag = false;

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (!initFlag) {
            initFlag = true;
            log.info("Load route-tag loaderBalancer");
        }
        if (bean instanceof NacosDiscoveryProperties) {
            String tags = System.getProperty("mrk.loadbalancer.tags");
            // 参数校验示例
            if (StringUtils.isNotBlank(tags)) {
                NacosDiscoveryProperties nacosDiscoveryProperties = (NacosDiscoveryProperties) bean;
                nacosDiscoveryProperties.getMetadata().put("routeTag", tags);
            }
        }
        return BeanPostProcessor.super.postProcessAfterInitialization(bean, beanName);
    }

}
