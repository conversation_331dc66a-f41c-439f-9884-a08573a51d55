package com.merach.sun.loadbalancer;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.cloud.nacos.balancer.NacosBalancer;
import com.alibaba.cloud.nacos.util.InetIPv6Utils;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.DefaultResponse;
import org.springframework.cloud.client.loadbalancer.EmptyResponse;
import org.springframework.cloud.client.loadbalancer.Request;
import org.springframework.cloud.client.loadbalancer.Response;
import org.springframework.cloud.loadbalancer.core.NoopServiceInstanceListSupplier;
import org.springframework.cloud.loadbalancer.core.ReactorServiceInstanceLoadBalancer;
import org.springframework.cloud.loadbalancer.core.SelectedInstanceCallback;
import org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier;
import reactor.core.publisher.Mono;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class RouteTagLoaderBalancerClient implements ReactorServiceInstanceLoadBalancer {


    private final String serviceId;

    private final ObjectProvider<ServiceInstanceListSupplier> serviceInstanceListSupplierProvider;

    // 路由指定routeTag
    private static final Set<String> ROUTE_TAGS = new HashSet<>();
    // 是否排除有routeTag的服务
    private static final Boolean ROUTE_TAGS_EXCLUDE;

    private final NacosDiscoveryProperties nacosDiscoveryProperties;

    private static final String IPV4_REGEX = "((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})(.((2(5[0-5]|[0-4]\\d))|[0-1]?\\d{1,2})){3}";




    @PostConstruct
    public void init() {
        String ip = nacosDiscoveryProperties.getIp();
    }

    public RouteTagLoaderBalancerClient(ObjectProvider<ServiceInstanceListSupplier> serviceInstanceListSupplierProvider,
                                        String serviceId,
                                        NacosDiscoveryProperties nacosDiscoveryProperties
    ) {
        this.serviceId = serviceId;
        this.serviceInstanceListSupplierProvider = serviceInstanceListSupplierProvider;
        this.nacosDiscoveryProperties = nacosDiscoveryProperties;
    }


    static {
        String tags = System.getProperty("mrk.loadbalancer.tags");
        ROUTE_TAGS_EXCLUDE = Boolean.valueOf(System.getProperty("mrk.loadbalancer.tags.exclude", "true"));
        if (tags == null) {
            log.info("no tag");
        } else {
            String[] tagList = tags.split(":");
            ROUTE_TAGS.addAll(Set.of(tagList));
        }

    }


    @Override
    public Mono<Response<ServiceInstance>> choose(Request request) {
        ServiceInstanceListSupplier supplier = serviceInstanceListSupplierProvider.getIfAvailable(NoopServiceInstanceListSupplier::new);
        return supplier.get(request).next().map(serviceInstances -> processInstanceResponse(supplier, serviceInstances));
    }

    private Response<ServiceInstance> processInstanceResponse(ServiceInstanceListSupplier supplier,
                                                              List<ServiceInstance> serviceInstances) {

        Response<ServiceInstance> serviceInstanceResponse = getInstanceResponse(serviceInstances);
        if (supplier instanceof SelectedInstanceCallback && serviceInstanceResponse.hasServer()) {
            ((SelectedInstanceCallback) supplier).selectedServiceInstance(serviceInstanceResponse.getServer());
        }
        return serviceInstanceResponse;
    }

    private Response<ServiceInstance> getInstanceResponse(
            List<ServiceInstance> serviceInstances) {
        if (serviceInstances.isEmpty()) {
            log.warn("No servers available for service: {}", this.serviceId);
            return new EmptyResponse();
        }

        try {
            String clusterName = this.nacosDiscoveryProperties.getClusterName();
            List<ServiceInstance> instancesToChoose = serviceInstances;
            if (StringUtils.isNotBlank(clusterName)) {
                List<ServiceInstance> sameClusterInstances = serviceInstances.stream()
                        .filter(serviceInstance -> {
                            String cluster = serviceInstance.getMetadata()
                                    .get("nacos.cluster");
                            return StringUtils.equals(cluster, clusterName);
                        }).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(sameClusterInstances)) {
                    instancesToChoose = sameClusterInstances;
                }
            } else {
                log.warn(
                        "A cross-cluster call occurs，name = {}, clusterName = {}, instance = {}",
                        serviceId, clusterName, serviceInstances);
            }
            instancesToChoose = this.filterInstanceByIpType(instancesToChoose);
            instancesToChoose = this.filterInstanceByRouteTag(instancesToChoose);
            if (CollectionUtils.isEmpty(instancesToChoose)) {
                return new EmptyResponse();
            }
            ServiceInstance instance = NacosBalancer
                    .getHostByRandomWeight3(instancesToChoose);

            return new DefaultResponse(instance);
        } catch (Exception e) {
            log.warn("RouteTagLoadBalancer error", e);
            return new EmptyResponse();
        }
    }

    private List<ServiceInstance> filterInstanceByIpType(List<ServiceInstance> instances) {
        return instances.stream()
                .filter(instance -> Pattern.matches(IPV4_REGEX, instance.getHost()))
                .collect(Collectors.toList());
    }

    private List<ServiceInstance> filterInstanceByRouteTag(List<ServiceInstance> instances) {
        if (!ROUTE_TAGS.isEmpty()) {
            instances = instances.stream().filter(instance -> {
                        Map<String, String> metadata = instance.getMetadata();
                        String routeTagStr = metadata.get("routeTag");
                        return StringUtils.isNotBlank(routeTagStr) &&
                                Arrays.stream(routeTagStr.split(","))
                                        .anyMatch(ROUTE_TAGS::contains);
                    }
            ).collect(Collectors.toList());
        } else if (ROUTE_TAGS_EXCLUDE) {
            instances = instances.stream().filter(instance -> {
                        Map<String, String> metadata = instance.getMetadata();
                        return !metadata.containsKey("routeTag");
                    }
            ).collect(Collectors.toList());
        }
        return instances;
    }


}
