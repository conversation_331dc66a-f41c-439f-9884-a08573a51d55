package com.merach.sun.rocketmq.callback;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;

/**
 * 默认回调
 *
 * <AUTHOR>
 * @date 2024/03/15 13:43:34
 */

@Slf4j
public class DefaultCallback implements SendCallback {

    public static final DefaultCallback SINGLE = new DefaultCallback();

    @Override
    public void onSuccess(SendResult sendResult) {

    }

    @Override
    public void onException(Throwable throwable) {
        log.warn("Failed to send message Exception", throwable);
    }
}
