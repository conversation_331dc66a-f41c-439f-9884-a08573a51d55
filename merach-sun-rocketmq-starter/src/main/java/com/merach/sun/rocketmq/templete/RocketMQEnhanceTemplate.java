package com.merach.sun.rocketmq.templete;

import com.alibaba.fastjson.JSON;
import com.merach.sun.rocketmq.callback.DefaultCallback;
import com.merach.sun.rocketmq.config.RocketMQEnhanceConfiguration;
import com.merach.sun.rocketmq.domain.BaseMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.apache.rocketmq.spring.support.RocketMQHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/03/13 16:58:15
 */

@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RocketMQEnhanceTemplate {

    private final RocketMQTemplate rocketMQTemplate;

    @Resource
    private RocketMQEnhanceConfiguration rocketMQEnhanceConfiguration;

    public RocketMQTemplate getRocketMQTemplate() {
        return rocketMQTemplate;
    }

    /**
     * 构建隔离后的 目的地
     *
     * @param topic
     * @param tag
     * @return {@link String}
     */
    public String buildDestination(String topic, String tag) {
        topic = buildTopic(topic);
        return String.join(":", topic, tag);
    }

    /**
     * 自动构建隔离后的 Topic
     *
     * @param topic 原始 Topic
     * @return {@link String}
     */
    private String buildTopic(String topic) {
        if (rocketMQEnhanceConfiguration.isEnabledIsolation() && StringUtils.hasText(rocketMQEnhanceConfiguration.getEnvironment())) {
            return String.join("_", topic, rocketMQEnhanceConfiguration.getEnvironment());
        }
        return topic;
    }

    /**
     * 发送同步消息
     */
    public <T extends BaseMessage> SendResult send(String topic, String tag, T message) {
        return send(buildDestination(topic, tag), message);
    }

    public <T extends BaseMessage> SendResult send(String destination, T message) {
        Assert.notNull(message.getKey(), "Message Key cannot be null");
        Message<T> sendMessage = MessageBuilder.withPayload(message).setHeader(RocketMQHeaders.KEYS, message.getKey()).build();
        SendResult sendResult = rocketMQTemplate.syncSend(destination, sendMessage);
        if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
            log.warn("[RocketMQEnhanceTemplate#send] Message send fail, destination={},message={}", destination, JSON.toJSONString(message));
        }
        return sendResult;
    }

    /**
     * 发送异步消息
     */
    public <T extends BaseMessage> void asyncSend(String topic, String tag, T message) {
        asyncSend(buildDestination(topic, tag), message);
    }


    public <T extends BaseMessage> void asyncSend(String destination, T message) {
        Assert.notNull(message.getKey(), "Message Key cannot be null");
        Message<T> sendMessage = MessageBuilder.withPayload(message).setHeader(RocketMQHeaders.KEYS, message.getKey()).build();
        rocketMQTemplate.asyncSend(destination, sendMessage, DefaultCallback.SINGLE);
    }


    /**
     * 发送延迟消息
     */
    public <T extends BaseMessage> SendResult send(String topic, String tag, T message, int delayLevel) {
        return send(buildDestination(topic, tag), message, delayLevel);
    }

    public <T extends BaseMessage> SendResult send(String destination, T message, int delayLevel) {
        Assert.notNull(message.getKey(), "Message Key cannot be null");
        Message<T> sendMessage = MessageBuilder.withPayload(message).setHeader(RocketMQHeaders.KEYS, message.getKey()).build();
        SendResult sendResult = rocketMQTemplate.syncSend(destination, sendMessage, 3000, delayLevel);
        if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
            log.warn("[RocketMQEnhanceTemplate#send] Message send fail, destination={},delayLevel={},message={}", destination, delayLevel, JSON.toJSONString(message));
        }
        return sendResult;
    }
}
