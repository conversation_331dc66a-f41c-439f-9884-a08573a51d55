package com.merach.sun.rocketmq.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

/**
 * 实例化消息监听者之前修改 Topic
 * <AUTHOR>
 * @date 2024/03/14 14:12:22
 */

@Slf4j
@Configuration
public class EnvironmentIsolationConfiguration implements BeanPostProcessor {
    private RocketMQEnhanceConfiguration rocketMQEnhanceConfiguration;

    public EnvironmentIsolationConfiguration(RocketMQEnhanceConfiguration rocketMQEnhanceConfiguration) {
        this.rocketMQEnhanceConfiguration = rocketMQEnhanceConfiguration;
    }


    /**
     * 在装载Bean之前实现参数修改
     *
     * @param bean
     * @param beanName
     * @return {@link Object}
     * @throws BeansException
     */
    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof DefaultRocketMQListenerContainer) {
            DefaultRocketMQListenerContainer container = (DefaultRocketMQListenerContainer) bean;
            if (rocketMQEnhanceConfiguration.isEnabledIsolation() && StringUtils.hasText(rocketMQEnhanceConfiguration.getEnvironment())) {
                container.setTopic(String.join("_", container.getTopic(), rocketMQEnhanceConfiguration.getEnvironment()));
            }
            return container;
        }
        return bean;
    }
}