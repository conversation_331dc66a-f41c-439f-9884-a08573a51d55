package com.merach.sun.rocketmq.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * RocketMQ 增强属性配置
 *
 * <AUTHOR>
 * @date 2024/03/13 17:44:22
 */
@Data
@ConfigurationProperties(prefix = "rocketmq.enhance")
public class RocketMQEnhanceConfiguration {

    /**
     *  是否启用环境隔离
     */
    private boolean enabledIsolation;

    /**
     *  隔离环境
     */
    private String environment;
}
