package com.merach.sun.rocketmq.autoconfigure;

import com.merach.sun.rocketmq.config.EnvironmentIsolationConfiguration;
import com.merach.sun.rocketmq.config.RocketMQEnhanceConfiguration;
import com.merach.sun.rocketmq.templete.RocketMQEnhanceTemplate;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(RocketMQEnhanceConfiguration.class)
public class RocketMQEnhanceAutoConfiguration {

    /**
     * 注入增强的RocketMQEnhanceTemplate
     */
    @Bean
    @ConditionalOnProperty(prefix = "rocketmq", value = {"name-server", "producer.group"})
    public RocketMQEnhanceTemplate rocketMQEnhanceTemplate(RocketMQTemplate rocketMQTemplate){
        return new RocketMQEnhanceTemplate(rocketMQTemplate);
    }


    /**
     * 环境隔离配置
     */
    @Bean
    @ConditionalOnProperty(name = "rocketmq.enhance.enabledIsolation", havingValue = "true")
    public EnvironmentIsolationConfiguration environmentSetup(RocketMQEnhanceConfiguration rocketMQEnhanceConfiguration){
        return new EnvironmentIsolationConfiguration(rocketMQEnhanceConfiguration);
    }

}