package com.merach.sun.exception.feign;

import com.merach.sun.common.lang.constant.BaseConstant;
import com.merach.sun.common.lang.exception.BusinessException;
import feign.Response;
import feign.RetryableException;
import feign.codec.ErrorDecoder;

import java.net.URLDecoder;
import java.nio.charset.Charset;

/**
 * feign异常拦截器，当从feign抛出异常时走这个对象.
 *
 * <AUTHOR>
 */
public class FeignClientErrorDecoder extends ErrorDecoder.Default {

    @Override
    public Exception decode(String methodKey, Response response) {
        Exception exception = super.decode(methodKey, response);
        int status = response.status();
        if (status == BaseConstant.STATUS_600.intValue()) {
            String code = response.headers().get(BaseConstant.CODE).iterator().next();
            String msg = response.headers().get(BaseConstant.MSG).iterator().next();
            msg = URLDecoder.decode(msg, Charset.defaultCharset());
            return new BusinessException(Integer.valueOf(code), msg);
        }

        // 如果是RetryableException，则返回继续重试
        if (exception instanceof RetryableException) {
            return exception;
        }

        return exception;
    }
}