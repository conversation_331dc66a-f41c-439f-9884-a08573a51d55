package com.merach.sun.exception.feign;

import feign.Feign;
import feign.codec.ErrorDecoder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * OpenFeignConfig
 *
 * <AUTHOR>
 */
@ConditionalOnClass(Feign.class)
@Configuration
public class OpenFeignConfig {

    /**
     * 自定义异常解码器
     *
     * @return FeignClientErrorDecoder
     */
    @Bean
    public ErrorDecoder errorDecoder() {
        return new FeignClientErrorDecoder();
    }

}