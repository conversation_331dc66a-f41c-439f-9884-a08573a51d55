package com.merach.sun.exception.feign;

import com.merach.sun.common.dto.SingleResponse;
import com.merach.sun.common.exception.BizException;
import com.merach.sun.common.lang.constant.BaseConstant;
import com.merach.sun.common.lang.exception.BaseException;
import com.merach.sun.common.lang.exception.BusinessException;
import com.merach.sun.common.lang.exception.ParamException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.Objects;

/**
 * 全局异常处理
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(value = BusinessException.class)
    public void handler(HttpServletResponse response, BusinessException ex) {
        this.setResponse(response, ex);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public void handlerMethodArgumentNotValidException(HttpServletResponse response, MethodArgumentNotValidException e) {
        String message = Objects.requireNonNull(e.getBindingResult().getFieldError()).getDefaultMessage();
        this.setResponse(response, new ParamException(message));
    }

    @ExceptionHandler(BindException.class)
    public void handlerBindException(HttpServletResponse response, BindException e) {
        String message = Objects.requireNonNull(e.getBindingResult().getFieldError()).getDefaultMessage();
        this.setResponse(response, new ParamException(message));
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public void handlerMissingServletRequestParameterException(HttpServletResponse response, MissingServletRequestParameterException e) {
        this.setResponse(response, new ParamException(e.getMessage()));
    }

    @ExceptionHandler(BizException.class)
    public SingleResponse handlerBizException(HttpServletResponse response, BizException ex) {
        return SingleResponse.buildFailure("500", String.format("业务异常，错误信息:%s", ex.getMessage()));
    }

    private void setResponse(HttpServletResponse response, BaseException e) {
        response.setStatus(BaseConstant.STATUS_600);
        response.addHeader(BaseConstant.CODE, e.getCode() + "");
        String msg = URLEncoder.encode(e.getMessage(), Charset.defaultCharset());
        response.addHeader(BaseConstant.MSG, msg);
    }

}